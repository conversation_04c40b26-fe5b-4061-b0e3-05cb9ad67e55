
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .border-border { border-color: hsl(var(--border)); }
  .bg-background { background-color: hsl(var(--background)); }
  .text-foreground { color: hsl(var(--foreground)); }
  .ring-ring { box-shadow: 0 0 0 3px hsl(var(--ring)); }
  .border-input { border-color: hsl(var(--input)); }
  .bg-card { background-color: hsl(var(--card)); }
  .text-card-foreground { color: hsl(var(--card-foreground)); }
  /* 依需求補齊其他自訂色 class */
}


@layer base {
  :root {
    --background: 220 20% 98%; /* Slightly off-white background */
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%; /* Keep cards white */
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217 91% 60%; /* A modern blue */
    --primary-foreground: 210 40% 98%; /* Light text on primary */

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%; /* Match new primary color */

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%; /* A modern blue for dark mode */
    --primary-foreground: 210 40% 98%; /* Light text on primary for dark mode */

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Noto Sans TC', sans-serif;
    line-height: 1.6; /* Improved readability */
  }
}

.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full text-white;
}

.status-processing {
  @apply bg-status-processing;
}

.status-completed {
  @apply bg-status-completed;
}

.status-canceled {
  @apply bg-status-canceled;
}

@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    font-size: 12pt;
    padding: 0;
    margin: 0;
  }
  
  .print-container {
    page-break-after: always;
  }
}

.print-only {
  display: none;
}
