# Cloudflare 配置指南

本文檔提供了詳細的 Cloudflare 配置步驟，以確保通過 Cloudflare 域名訪問後台時能夠即時獲取最新數據。

## 目錄

1. [問題概述](#問題概述)
2. [頁面規則配置](#頁面規則配置)
3. [快取配置](#快取配置)
4. [開發者模式](#開發者模式)
5. [清除快取](#清除快取)
6. [監控與調試](#監控與調試)
7. [常見問題解答](#常見問題解答)

## 問題概述

通過 Cloudflare 域名訪問後台時，可能會遇到數據不即時更新的問題。這是因為 Cloudflare 默認會快取靜態資源和 API 響應，導致即使數據庫中的數據已更新，前端仍然顯示舊數據。

主要原因包括：

1. Cloudflare 的邊緣快取
2. 瀏覽器快取
3. 伺服器端快取
4. HTTP 快取標頭不完整

## 頁面規則配置

### 1. 禁用 API 路徑的快取

1. 登錄 Cloudflare 控制面板
2. 選擇您的域名
3. 點擊 "頁面規則" (Page Rules)
4. 點擊 "創建頁面規則" (Create Page Rule)
5. 在 URL 欄位中輸入：`*yourdomain.com/api/*`
6. 添加以下設置：
   - 快取級別 (Cache Level): 繞過 (Bypass)
   - 瀏覽器快取 TTL (Browser Cache TTL): 0 秒
   - 邊緣快取 TTL (Edge Cache TTL): 0 秒
   - 始終使用 HTTPS: 開啟
7. 點擊 "保存並部署" (Save and Deploy)

### 2. 禁用開發工具路徑的快取

1. 創建另一個頁面規則
2. URL 欄位中輸入：`*yourdomain.com/sheet-order-dashboard-main/api/*`
3. 添加與上面相同的設置
4. 保存並部署

### 3. 設置 API 響應標頭

如果您有權限修改 Cloudflare 的 Workers，可以創建一個 Worker 來添加額外的響應標頭：

```javascript
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // 獲取原始響應
  const response = await fetch(request)
  
  // 檢查是否是 API 請求
  const url = new URL(request.url)
  if (url.pathname.includes('/api/') || url.pathname.includes('/sheet-order-dashboard-main/api/')) {
    // 創建新的響應對象，添加快取控制標頭
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: addCacheHeaders(response.headers)
    })
  }
  
  return response
}

function addCacheHeaders(headers) {
  const newHeaders = new Headers(headers)
  newHeaders.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0, s-maxage=0')
  newHeaders.set('Pragma', 'no-cache')
  newHeaders.set('Expires', '0')
  newHeaders.set('Surrogate-Control', 'no-store')
  return newHeaders
}
```

## 快取配置

### 1. 瀏覽器快取設置

在 Cloudflare 控制面板中：

1. 點擊 "快取" (Cache)
2. 找到 "瀏覽器快取 TTL" (Browser Cache TTL)
3. 對於 API 路徑，設置為 "繞過" (Bypass)

### 2. 邊緣快取設置

1. 點擊 "快取" (Cache)
2. 找到 "邊緣快取 TTL" (Edge Cache TTL)
3. 對於 API 路徑，設置為 0 秒

### 3. 快取規則

1. 點擊 "快取" (Cache)
2. 點擊 "快取規則" (Cache Rules)
3. 創建新規則：
   - 如果 URL 路徑包含 `/api/` 或 `/sheet-order-dashboard-main/api/`
   - 則設置快取級別為 "繞過" (Bypass)

## 開發者模式

在開發和測試期間，可以暫時啟用開發者模式，完全繞過 Cloudflare 的快取：

1. 在 Cloudflare 控制面板中，點擊 "快取" (Cache)
2. 在 "配置" (Configuration) 部分，找到 "開發者模式" (Development Mode)
3. 將開關設置為 "開啟" (On)
4. 開發者模式會自動在 3 小時後關閉

**注意**：開發者模式會對所有頁面禁用快取，可能會增加源伺服器的負載。

## 清除快取

如果您仍然遇到快取問題，可以嘗試清除 Cloudflare 的快取：

### 1. 清除所有快取

1. 在 Cloudflare 控制面板中，點擊 "快取" (Cache)
2. 點擊 "清除所有快取" (Purge Everything)
3. 確認操作

### 2. 自定義清除

1. 點擊 "自定義清除" (Custom Purge)
2. 輸入以下 URL 模式：
   - `*yourdomain.com/api/*`
   - `*yourdomain.com/sheet-order-dashboard-main/api/*`
3. 點擊 "清除" (Purge)

### 3. 通過 API 清除快取

您也可以通過 Cloudflare API 清除快取：

```bash
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
     -H "Authorization: Bearer {api_token}" \
     -H "Content-Type: application/json" \
     --data '{"files":["https://yourdomain.com/api/get_orders_from_sheet.php"]}'
```

## 監控與調試

### 1. 檢查快取狀態

在瀏覽器的開發者工具中：

1. 打開 Network 標籤
2. 發送一個 API 請求
3. 檢查響應標頭中的 `CF-Cache-Status`：
   - `BYPASS`: 快取已被繞過
   - `MISS`: 內容未被快取
   - `HIT`: 內容從快取中提供
   - `EXPIRED`: 快取已過期

### 2. 使用 API 診斷工具

訪問 `/api/check_api_path.php` 檢查 API 路徑和環境配置。

### 3. 添加調試標頭

在 API 響應中添加調試標頭：

```php
header('X-Cache-Debug: ' . (isset($_SERVER['HTTP_CF_CONNECTING_IP']) ? 'CF' : 'Direct'));
header('X-Request-Time: ' . time());
```

## 常見問題解答

### Q: 即使配置了頁面規則，API 響應仍然被快取

A: 確保：
1. 頁面規則的 URL 模式正確
2. API 響應包含正確的快取控制標頭
3. 清除了現有的快取
4. 檢查是否有其他頁面規則覆蓋了您的設置

### Q: 刪除訂單後，列表沒有立即更新

A: 這可能是因為：
1. 前端快取未清除
2. Cloudflare 仍在快取 API 響應
3. 伺服器端快取未正確刪除

解決方案：
1. 確保在刪除訂單後調用 `clearOrderCache()`
2. 在 API 請求中添加隨機參數
3. 檢查伺服器端快取文件是否被正確刪除

### Q: 通過 Cloudflare 訪問比直接訪問慢

A: 這可能是因為：
1. Cloudflare 的 DNS 解析時間
2. Cloudflare 的安全檢查
3. 您的源伺服器與 Cloudflare 節點之間的連接速度

解決方案：
1. 啟用 Cloudflare 的 Argo Smart Routing
2. 優化源伺服器的響應時間
3. 考慮使用 Cloudflare Workers 來優化響應
