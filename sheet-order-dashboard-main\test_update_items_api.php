<?php
/**
 * 測試 update_order_items.php API 的簡單腳本
 */

// 測試資料
$testData = [
    'id' => '2', // 假設測試第2行的訂單
    'items' => [
        [
            'product' => '原味蘿蔔糕',
            'quantity' => 2,
            'price' => 250,
            'subtotal' => 500
        ],
        [
            'product' => '芋頭粿',
            'quantity' => 1,
            'price' => 350,
            'subtotal' => 350
        ]
    ],
    'total' => 850,
    'timestamp' => time(),
    'nonce' => uniqid()
];

// 發送 POST 請求到 API
$url = 'http://localhost/sheet-order-dashboard-main/api/update_order_items.php';
$postData = json_encode($testData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($postData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status Code: $httpCode\n";
echo "Response: $response\n";

// 解析回應
$result = json_decode($response, true);
if ($result) {
    echo "Success: " . ($result['success'] ? 'true' : 'false') . "\n";
    if (isset($result['message'])) {
        echo "Message: " . $result['message'] . "\n";
    }
} else {
    echo "Failed to parse JSON response\n";
}
?>
