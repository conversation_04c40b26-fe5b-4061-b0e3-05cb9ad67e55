# 訂單商品編輯功能測試指南

## 🧪 測試環境準備

### 1. 啟動開發環境
```bash
# 在 sheet-order-dashboard-main 目錄下執行
npm run dev
```

### 2. 確認服務運行
- 前端：http://localhost:5173
- 後端 API：確保 XAMPP Apache 服務運行

## 🔍 功能測試步驟

### 測試 1：基本商品編輯功能

#### 步驟：
1. 開啟瀏覽器訪問 http://localhost:5173
2. 登入管理後台
3. 在訂單列表中點擊任一訂單
4. 在訂單詳情對話框中，找到「訂購商品」區域
5. 點擊「編輯商品」按鈕

#### 預期結果：
- ✅ 商品編輯對話框開啟
- ✅ 顯示現有商品清單
- ✅ 每個商品項目顯示：商品名稱、單價、數量、小計
- ✅ 顯示當前總金額

### 測試 2：商品數量調整

#### 步驟：
1. 在商品編輯對話框中
2. 點擊任一商品的「+」按鈕
3. 點擊任一商品的「-」按鈕
4. 直接在數量輸入框中輸入數字

#### 預期結果：
- ✅ 數量正確增加/減少
- ✅ 小計自動重新計算
- ✅ 總金額即時更新
- ✅ 最小數量限制為 1（不能減少到 0）

### 測試 3：新增商品

#### 步驟：
1. 在「新增商品」區域
2. 從下拉選單選擇商品（原味蘿蔔糕、芋頭粿、港式蘿蔔糕）
3. 點擊「新增」按鈕

#### 預期結果：
- ✅ 新商品出現在商品清單中
- ✅ 如果商品已存在，數量自動增加 1
- ✅ 總金額正確更新
- ✅ 顯示成功提示訊息

### 測試 4：刪除商品

#### 步驟：
1. 點擊任一商品項目右側的垃圾桶圖示

#### 預期結果：
- ✅ 商品從清單中移除
- ✅ 總金額重新計算
- ✅ 顯示移除成功訊息

### 測試 5：儲存變更

#### 步驟：
1. 完成商品編輯後
2. 點擊「儲存變更」按鈕

#### 預期結果：
- ✅ 顯示「儲存中...」狀態
- ✅ 成功後顯示成功訊息
- ✅ 編輯對話框關閉
- ✅ 訂單詳情頁面顯示更新後的商品和金額
- ✅ Google Sheets 中的資料已更新

### 測試 6：取消編輯

#### 步驟：
1. 進行一些編輯操作
2. 點擊「取消」按鈕

#### 預期結果：
- ✅ 編輯對話框關閉
- ✅ 所有變更被丟棄
- ✅ 訂單詳情保持原始狀態

## 🚨 錯誤情況測試

### 測試 7：空訂單驗證

#### 步驟：
1. 刪除所有商品項目
2. 嘗試儲存

#### 預期結果：
- ✅ 顯示錯誤訊息：「訂單不能為空」
- ✅ 無法儲存

### 測試 8：網路錯誤處理

#### 步驟：
1. 暫時停止 XAMPP Apache 服務
2. 嘗試儲存變更

#### 預期結果：
- ✅ 顯示網路錯誤訊息
- ✅ 編輯狀態保持，可以重試

## 🔧 API 測試

### 測試 9：直接 API 測試

#### 步驟：
1. 執行測試腳本：
```bash
php test_update_items_api.php
```

#### 預期結果：
- ✅ HTTP Status Code: 200
- ✅ Response: {"success":true,...}

## 📊 Google Sheets 驗證

### 測試 10：資料同步驗證

#### 步驟：
1. 完成商品編輯並儲存
2. 直接查看 Google Sheets

#### 預期結果：
- ✅ I欄（購買項目）格式：「原味蘿蔔糕 x 2, 芋頭粿 x 1」
- ✅ J欄（金額）顯示正確的總金額數字

## 🎨 UI/UX 測試

### 測試 11：響應式設計

#### 步驟：
1. 在不同螢幕尺寸下測試
2. 使用瀏覽器開發者工具模擬行動裝置

#### 預期結果：
- ✅ 對話框在小螢幕上正常顯示
- ✅ 按鈕和輸入框大小適當
- ✅ 滾動功能正常

### 測試 12：載入狀態

#### 步驟：
1. 觀察各種載入狀態

#### 預期結果：
- ✅ 儲存時按鈕顯示「儲存中...」
- ✅ 載入期間按鈕被禁用
- ✅ 載入完成後恢復正常

## 🐛 常見問題排除

### 問題 1：API 錯誤「找不到必要的欄位」
**解決方案**：確認 Google Sheets 結構正確，I欄為購買項目，J欄為金額

### 問題 2：Dialog 警告訊息
**解決方案**：已添加 DialogDescription，警告應該消失

### 問題 3：商品價格不正確
**解決方案**：檢查 AVAILABLE_PRODUCTS 常數中的價格設定

## ✅ 測試檢查清單

- [ ] 基本商品編輯功能
- [ ] 商品數量調整
- [ ] 新增商品
- [ ] 刪除商品
- [ ] 儲存變更
- [ ] 取消編輯
- [ ] 空訂單驗證
- [ ] 網路錯誤處理
- [ ] API 直接測試
- [ ] Google Sheets 同步
- [ ] 響應式設計
- [ ] 載入狀態

## 📝 測試報告

測試完成後，請記錄：
- 測試日期：
- 測試環境：
- 發現的問題：
- 建議改進：

---

**注意**：如果發現任何問題，請檢查瀏覽器控制台的錯誤訊息，並確認 Google Sheets API 權限正常。
