
<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>融氏古早味蘿蔔糕 - 訂單管理後台</title>
    <meta name="description" content="蘿蔔糕訂購系統管理後台，查看訂單、列印訂單、下載CSV" />
    <meta name="author" content="融氏古早味" />

    <meta property="og:title" content="蘿蔔糕訂購系統 - 管理後台" />
    <meta property="og:description" content="蘿蔔糕訂購系統管理後台，查看訂單、列印訂單、下載CSV" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/public/LOGO-1.webp" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- 動態設置 API 基礎路徑 -->
    <script>
      window.API_CONFIG = {
        // 檢測當前環境
        getApiBase: function() {
          const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
          const port = window.location.port;

          // 本地開發環境
          if (isLocalhost && port === '8080') {
            return '/sheet-order-dashboard-main/api';
          }

          // 生產環境或其他情況
          return '/api';
        }
      };
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- GPT Engineer script - only load in localhost development environment -->
    <script>
      // Only load GPT Engineer script in localhost development environment
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const script = document.createElement('script');
        script.src = 'https://cdn.gpteng.co/gptengineer.js';
        script.type = 'module';
        document.head.appendChild(script);
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
