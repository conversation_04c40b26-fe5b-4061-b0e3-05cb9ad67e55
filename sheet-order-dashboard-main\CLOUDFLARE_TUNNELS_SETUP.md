# Cloudflare Tunnels 設定指南

## 🌐 環境說明

**Cloudflare Tunnels 設定**：
- 域名：`https://node.767780.xyz/`
- 指向：`localhost:8080`
- 文檔根目錄：`/sheet-order-dashboard-main`

**重要**：域名 `https://node.767780.xyz/` 直接指向 `/sheet-order-dashboard-main` 目錄，所以：
- 訪問 `https://node.767780.xyz/` = 訪問 `/sheet-order-dashboard-main/`
- 訪問 `https://node.767780.xyz/api/` = 訪問 `/sheet-order-dashboard-main/api/`

## 🔧 API 路徑邏輯

已修改 `orderService.ts` 和 `customerService.ts` 中的 API 路徑邏輯：

```typescript
const getApiBase = () => {
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const port = window.location.port;

  // 本地開發環境（localhost:8080 指向 htdocs，需要完整路徑）
  if (isLocalhost && port === '8080') {
    return '/sheet-order-dashboard-main/api';
  }

  // Cloudflare Tunnels 環境（node.767780.xyz 直接指向 sheet-order-dashboard-main 目錄）
  // 所以 API 路徑就是 /api
  return '/api';
};
```

### 2. Cloudflare 設定建議

#### A. 快取規則設定

在 Cloudflare Dashboard 中設定以下快取規則：

1. **API 端點不快取**：
   - URL 模式：`*node.767780.xyz/api/*`
   - 快取等級：Bypass
   - 邊緣快取 TTL：Respect origin

2. **動態內容不快取**：
   - URL 模式：`*node.767780.xyz/*.php`
   - 快取等級：Bypass（針對 PHP 檔案）

#### B. Page Rules 設定

1. **API 路徑規則**：
   ```
   URL: node.767780.xyz/api/*
   設定：
   - Cache Level: Bypass
   - Disable Apps
   - Disable Performance
   ```

2. **主應用程式規則**：
   ```
   URL: node.767780.xyz/*
   設定：
   - Browser Cache TTL: 4 hours
   - Cache Level: Standard
   ```

#### C. 安全性設定

1. **SSL/TLS 模式**：設定為 "Full" 或 "Full (strict)"
2. **Always Use HTTPS**：開啟
3. **HSTS**：建議開啟

### 3. 本地 XAMPP 設定

#### A. Apache 虛擬主機設定

在 `httpd-vhosts.conf` 中添加：

```apache
<VirtualHost *:8080>
    DocumentRoot "D:/xampp/htdocs"
    ServerName localhost

    # 允許跨域請求
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

    # 禁用快取（開發環境）
    Header always set Cache-Control "no-cache, no-store, must-revalidate"
    Header always set Pragma "no-cache"
    Header always set Expires "0"

    <Directory "D:/xampp/htdocs">
        AllowOverride All
        Require all granted

        # 處理 OPTIONS 請求
        RewriteEngine On
        RewriteCond %{REQUEST_METHOD} OPTIONS
        RewriteRule ^(.*)$ $1 [R=200,L]
    </Directory>
</VirtualHost>
```

#### B. .htaccess 設定

在 `sheet-order-dashboard-main` 目錄下創建 `.htaccess`：

```apache
# 啟用重寫引擎
RewriteEngine On

# 處理 CORS 預檢請求
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 設定 CORS 標頭
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# 禁用快取（API 檔案）
<FilesMatch "\.(php)$">
    Header always set Cache-Control "no-cache, no-store, must-revalidate"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
</FilesMatch>

# 允許訪問 API 目錄
<Directory "api">
    AllowOverride All
    Require all granted
</Directory>
```

### 4. 前端快取破壞機制

已在 API 請求中添加多重快取破壞參數：

```typescript
const url = new URL(`${window.location.origin}${API_BASE}/update_order_items.php`);
url.searchParams.append('_', timestamp.toString());
url.searchParams.append('nonce', nonce);
url.searchParams.append('v', '1.1');
url.searchParams.append('random', Math.random().toString(36).substring(2, 15));
```

### 5. 測試步驟

#### A. 本地測試

1. 確保 XAMPP Apache 在 8080 端口運行
2. 訪問 `http://localhost:8080/sheet-order-dashboard-main/`
3. 測試 API 功能是否正常

#### B. Cloudflare Tunnels 測試

1. 啟動 Cloudflare Tunnel：
   ```bash
   cloudflared tunnel run <tunnel-name>
   ```

2. 訪問 `https://node.767780.xyz/`
3. 檢查瀏覽器控制台：
   - API 路徑應顯示：`/api`
   - 不應有 404 錯誤

#### C. API 端點測試

直接測試 API 端點：

```bash
# 測試訂單 API
curl "https://node.767780.xyz/api/get_orders_from_sheet.php?_=123456&nonce=test"

# 測試商品更新 API
curl -X POST "https://node.767780.xyz/api/update_order_items.php" \
  -H "Content-Type: application/json" \
  -d '{"id":"2","items":[{"product":"原味蘿蔔糕","quantity":1,"price":250,"subtotal":250}],"total":250}'

# 測試工具頁面
curl "https://node.767780.xyz/test_api_paths.html"
```

### 6. 故障排除

#### A. 常見問題

1. **404 錯誤**：
   - 檢查 API 路徑是否正確
   - 確認檔案權限設定
   - 檢查 .htaccess 配置

2. **CORS 錯誤**：
   - 檢查 Apache 標頭設定
   - 確認 Cloudflare SSL 模式
   - 檢查安全性設定

3. **快取問題**：
   - 清除 Cloudflare 快取
   - 檢查快取規則設定
   - 使用強制重新整理 (Ctrl+F5)

#### B. 除錯工具

1. **瀏覽器開發者工具**：
   - Network 標籤檢查請求
   - Console 標籤查看錯誤訊息

2. **Cloudflare Analytics**：
   - 檢查請求統計
   - 查看錯誤率

3. **Apache 日誌**：
   - 檢查 `error.log`
   - 檢查 `access.log`

### 7. 效能優化建議

1. **靜態資源快取**：
   - CSS/JS 檔案設定長期快取
   - 圖片資源使用 CDN

2. **API 回應優化**：
   - 使用 gzip 壓縮
   - 最小化 JSON 回應

3. **連接優化**：
   - 啟用 HTTP/2
   - 使用 Cloudflare 的效能功能

## 🎯 總結

通過以上設定，您的專案應該能夠：

✅ 在 Cloudflare Tunnels 環境下正常運行
✅ 正確處理 API 請求
✅ 避免快取相關問題
✅ 提供良好的使用者體驗

如果仍有問題，請檢查瀏覽器控制台的具體錯誤訊息，並根據錯誤類型進行相應的調整。
