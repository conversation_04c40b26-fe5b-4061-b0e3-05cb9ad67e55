# AGENT.md - Sheet Order Dashboard

## Build/Lint Commands
- `npm run dev` - Start development server
- `npm run build` - Production build
- `npm run build:dev` - Development build
- `npm run lint` - Lint the codebase
- `npm run preview` - Preview production build

## Code Style Guidelines
- Use TypeScript throughout the codebase
- Use React functional components with hooks
- Import paths use `@/` alias for src directory (e.g., `import { Order } from '@/types/order'`)
- Component names use PascalCase, functions/variables use camelCase
- Type declarations for function parameters and return values
- TypeScript configuration allows null and undefined values (strictNullChecks disabled)
- CSS uses Tailwind with shadcn UI components

## Project Structure
- `/src/components` - React components
- `/src/types` - TypeScript type definitions
- `/src/utils` - Utility functions
- `/src/services` - API services
- `/src/pages` - Page components
- `/src/hooks` - Custom React hooks

## Special Note (.windsurfrules)
- Comments should be in Traditional Chinese
- Detailed comments required for PHP, JS, HTML, SQL code explaining logic, parameters, and validation rules
- Files should not exceed 430 lines, split into multiple files if necessary