# Cloudflare Tunnels 路徑修正說明

## 🔍 問題發現

感謝您的指正！我之前理解錯了 Cloudflare Tunnels 的設定方式。

### 錯誤理解
我原本以為：
- `https://node.767780.xyz/` 指向 `localhost:8080/htdocs/`
- 需要完整路徑：`/sheet-order-dashboard-main/api/`

### 正確理解
實際上您的設定是：
- `https://node.767780.xyz/` **直接指向** `localhost:8080/sheet-order-dashboard-main/`
- 所以 API 路徑應該是：`/api/`

## ✅ 已修正的檔案

### 1. orderService.ts
```typescript
const getApiBase = () => {
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const port = window.location.port;

  // 本地開發環境（localhost:8080 指向 htdocs，需要完整路徑）
  if (isLocalhost && port === '8080') {
    return '/sheet-order-dashboard-main/api';
  }

  // Cloudflare Tunnels 環境（node.767780.xyz 直接指向 sheet-order-dashboard-main 目錄）
  // 所以 API 路徑就是 /api
  return '/api';
};
```

### 2. customerService.ts
同樣的邏輯修正

### 3. test_api_paths.html
測試工具也已更新相同邏輯

### 4. CLOUDFLARE_TUNNELS_SETUP.md
更新了所有相關的 URL 路徑和設定說明

## 🎯 現在的路徑邏輯

### 本地開發環境
- URL: `http://localhost:8080/sheet-order-dashboard-main/`
- API 路徑: `/sheet-order-dashboard-main/api`
- 實際 API URL: `http://localhost:8080/sheet-order-dashboard-main/api/`

### Cloudflare Tunnels 環境
- URL: `https://node.767780.xyz/`
- API 路徑: `/api`
- 實際 API URL: `https://node.767780.xyz/api/`

## 🧪 測試方法

### 1. 使用測試工具
訪問：`https://node.767780.xyz/test_api_paths.html`

預期結果：
- 計算出的 API 路徑：`/api`
- 所有 API 測試應該成功

### 2. 檢查控制台
訪問：`https://node.767780.xyz/`

預期控制台輸出：
```
當前 API 路徑: /api
客戶服務 API 路徑: /api
```

### 3. 測試訂單編輯功能
1. 開啟任一訂單詳情
2. 點擊「編輯商品」
3. 進行編輯並儲存
4. 應該不再出現 404 錯誤

## 🔧 Cloudflare 設定建議

### Page Rules
```
URL: node.767780.xyz/api/*
設定：
- Cache Level: Bypass
- Disable Apps
- Disable Performance
```

### 快取規則
```
URL 模式: *node.767780.xyz/api/*
快取等級: Bypass
```

## 📝 重要提醒

1. **路徑對應關係**：
   - `https://node.767780.xyz/` = `/sheet-order-dashboard-main/`
   - `https://node.767780.xyz/api/` = `/sheet-order-dashboard-main/api/`
   - `https://node.767780.xyz/test_api_paths.html` = `/sheet-order-dashboard-main/test_api_paths.html`

2. **API 請求**：
   - 前端請求：`/api/get_orders_from_sheet.php`
   - 實際訪問：`https://node.767780.xyz/api/get_orders_from_sheet.php`
   - 對應檔案：`/sheet-order-dashboard-main/api/get_orders_from_sheet.php`

3. **測試確認**：
   - 控制台應顯示：`當前 API 路徑: /api`
   - 不應再有 404 錯誤
   - 訂單編輯功能應正常運作

## 🎉 總結

修正後的程式碼現在能正確處理您的 Cloudflare Tunnels 設定：

✅ **本地環境**：`localhost:8080` → API 路徑 `/sheet-order-dashboard-main/api`  
✅ **Cloudflare 環境**：`node.767780.xyz` → API 路徑 `/api`  
✅ **自動檢測**：根據主機名稱自動選擇正確路徑  
✅ **測試工具**：提供完整的測試和除錯功能  

感謝您的耐心指正，現在應該可以正常運作了！
