import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0",
    allowedHosts: ['node.767780.xyz', 'lopokao.767780.xyz', 'localhost', '127.0.0.1', '************' ,'************:8080' ,'767780.xyz'],
    cors: true,
    headers: {
      'Content-Security-Policy': "frame-ancestors 'self' http://localhost http://127.0.0.1 http://*********** http://************ http://************:8080 https://lopokao.767780.xyz https://node.767780.xyz https://cdn.gpteng.co *",
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
    },
    proxy: {
      '/api': {
        target: 'http://localhost',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '/sheet-order-dashboard-main/api'),
      },
      '/sheet-order-dashboard-main/api': {
        target: 'http://localhost',
        changeOrigin: true,
        secure: false,
      }
    },
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
