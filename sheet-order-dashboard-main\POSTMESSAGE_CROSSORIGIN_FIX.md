# PostMessage 跨域錯誤修復指南

## 🚨 問題現象

當透過 Cloudflare 域名 `https://node.767780.xyz/` 訪問專案時，瀏覽器控制台出現大量跨域 postMessage 錯誤：

```
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<網址>') does not match the recipient window's origin ('<網址>').
```

## 🔍 問題分析

### 根本原因
1. **GPT Engineer 工具列腳本**：`https://cdn.gpteng.co/gptengineer.js` 嘗試與不同來源的視窗進行通訊
2. **跨域限制**：當透過 Cloudflare 域名訪問時，與本地開發環境的來源不匹配
3. **postMessage 安全限制**：瀏覽器阻止了不同來源之間的 postMessage 通訊

### 錯誤來源
- `gptengineer.js:1` - GPT Engineer 工具列腳本
- `admin.php:472-473` - 跨域限制處理程式碼
- `admin.php:662` - 樣式注入訊息發送

## 🛠️ 解決方案

### ✅ 已實施的修復

#### 1. 條件式載入 GPT Engineer 腳本
**檔案**：`index.html`

```javascript
// 只在 localhost 開發環境載入 GPT Engineer 腳本
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  const script = document.createElement('script');
  script.src = 'https://cdn.gpteng.co/gptengineer.js';
  script.type = 'module';
  document.head.appendChild(script);
}
```

**優點**：
- ✅ 保留開發環境的 GPT Engineer 功能
- ✅ 避免生產環境的跨域錯誤
- ✅ 不影響專案核心功能

#### 2. 更新 Vite 配置
**檔案**：`vite.config.ts`

```typescript
headers: {
  'Content-Security-Policy': "frame-ancestors 'self' http://localhost http://127.0.0.1 http://*********** http://************ http://************:8080 https://767780.xyz https://node.767780.xyz https://cdn.gpteng.co *",
  'Cross-Origin-Embedder-Policy': 'unsafe-none',
  'Cross-Origin-Opener-Policy': 'unsafe-none',
}
```

**改進**：
- ✅ 新增 `https://node.767780.xyz` 到允許的來源
- ✅ 新增 `https://cdn.gpteng.co` 到 CSP 白名單
- ✅ 設置跨域政策為寬鬆模式

#### 3. 重新建置專案
執行 `npm run build` 以應用所有變更。

## 🧪 測試結果

### 預期效果
- ✅ 透過 `https://node.767780.xyz/` 訪問時不再出現 postMessage 錯誤
- ✅ 本地開發環境 `http://localhost:8080` 仍可使用 GPT Engineer 工具列
- ✅ 所有專案功能正常運作

### 驗證步驟
1. 清除瀏覽器快取
2. 訪問 `https://node.767780.xyz/`
3. 開啟瀏覽器開發者工具
4. 檢查控制台是否還有 postMessage 錯誤

## 🔄 替代方案

### 方案 A：完全移除 GPT Engineer 腳本
```html
<!-- 完全註解掉 GPT Engineer 腳本 -->
<!-- <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script> -->
```

### 方案 B：使用環境變數控制
```javascript
// 使用環境變數控制是否載入
if (import.meta.env.DEV) {
  // 只在開發模式載入
}
```

## 📋 注意事項

1. **開發環境**：GPT Engineer 工具列仍可在 localhost 使用
2. **生產環境**：透過 Cloudflare 域名訪問時不會載入 GPT Engineer 腳本
3. **功能影響**：此修復不影響專案的核心功能
4. **未來更新**：如果 GPT Engineer 更新其跨域政策，可能需要調整此修復

## 🔧 故障排除

### 如果錯誤仍然存在
1. 確認瀏覽器快取已清除
2. 檢查 `index.html` 中的條件式載入邏輯
3. 驗證 `vite.config.ts` 中的 CSP 設定
4. 重新建置專案：`npm run build`

### 如果需要在生產環境使用 GPT Engineer
修改 `index.html` 中的條件：
```javascript
// 允許特定域名載入 GPT Engineer
if (window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname === 'your-production-domain.com') {
  // 載入腳本
}
```

---

**修復完成時間**：2024年12月
**影響範圍**：跨域 postMessage 錯誤
**狀態**：✅ 已解決
