<?php
/**
 * ID重排序功能測試腳本
 * 用於測試刪除訂單後的ID重排序功能
 */

require 'D:/xampp/htdocs/vendor/autoload.php';
use Google\Client;
use Google\Service\Sheets;

// 設定
$spreadsheetId = '10MMALrfBonchPGjb-ps6Knw7MV6lllrrKRCTeafCIuo';
$sheetName = 'Sheet1';

/**
 * 測試ID重排序功能
 */
function testIdReorder() {
    global $spreadsheetId, $sheetName;
    
    try {
        $client = new Client();
        $client->setApplicationName('ID重排序測試');
        $client->setScopes([Sheets::SPREADSHEETS]);
        $client->setAuthConfig(__DIR__ . '/service-account-key2.json');
        $client->setAccessType('offline');

        $service = new Sheets($client);
        
        echo "🔍 開始測試ID重排序功能...\n\n";
        
        // 1. 讀取當前資料
        echo "1. 讀取當前Google Sheets資料:\n";
        $range = $sheetName;
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $rows = $response->getValues();
        
        if (empty($rows)) {
            echo "   ❌ 工作表中沒有資料\n";
            return;
        }
        
        echo "   📊 總行數: " . count($rows) . " (包含標題行)\n";
        echo "   📋 資料行數: " . (count($rows) - 1) . "\n\n";
        
        // 2. 顯示當前ID狀況
        echo "2. 當前訂單ID狀況:\n";
        for ($i = 1; $i < count($rows); $i++) {
            if (!isset($rows[$i][1]) || trim($rows[$i][1]) === '') {
                continue; // 跳過空白行
            }
            
            $currentId = isset($rows[$i][13]) ? $rows[$i][13] : '未設定';
            $expectedId = $i;
            $customerName = $rows[$i][1] ?? '未知';
            
            $status = ($currentId == $expectedId) ? '✅' : '❌';
            echo "   行 " . ($i + 1) . ": ID={$currentId}, 期望ID={$expectedId} {$status} (客戶: {$customerName})\n";
        }
        echo "\n";
        
        // 3. 模擬重排序功能
        echo "3. 執行ID重排序測試:\n";
        $result = simulateReorderIds($service, $spreadsheetId, $sheetName);
        
        if ($result['success']) {
            echo "   ✅ 重排序成功!\n";
            echo "   📝 " . $result['message'] . "\n";
            echo "   🔢 更新行數: " . $result['updated_rows'] . "\n";
        } else {
            echo "   ❌ 重排序失敗: " . $result['message'] . "\n";
        }
        echo "\n";
        
        // 4. 驗證結果
        echo "4. 驗證重排序結果:\n";
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $newRows = $response->getValues();
        
        $allCorrect = true;
        for ($i = 1; $i < count($newRows); $i++) {
            if (!isset($newRows[$i][1]) || trim($newRows[$i][1]) === '') {
                continue; // 跳過空白行
            }
            
            $currentId = isset($newRows[$i][13]) ? $newRows[$i][13] : '未設定';
            $expectedId = $i;
            $customerName = $newRows[$i][1] ?? '未知';
            
            if ($currentId != $expectedId) {
                $allCorrect = false;
                echo "   ❌ 行 " . ($i + 1) . ": ID={$currentId}, 期望ID={$expectedId} (客戶: {$customerName})\n";
            } else {
                echo "   ✅ 行 " . ($i + 1) . ": ID={$currentId} 正確 (客戶: {$customerName})\n";
            }
        }
        
        echo "\n";
        if ($allCorrect) {
            echo "🎉 所有ID都已正確重排序!\n";
        } else {
            echo "⚠️  部分ID仍需調整\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 測試過程中發生錯誤: " . $e->getMessage() . "\n";
    }
}

/**
 * 模擬重排序ID功能
 */
function simulateReorderIds($service, $spreadsheetId, $sheetName) {
    try {
        // 重新獲取所有資料
        $range = $sheetName;
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $rows = $response->getValues();
        
        if (empty($rows) || count($rows) <= 1) {
            return [
                'success' => true,
                'message' => '沒有需要重新排序的資料',
                'updated_rows' => 0
            ];
        }
        
        // 準備批量更新的資料
        $updateData = [];
        $updatedCount = 0;
        
        // 從第二行開始（跳過標題行），重新分配ID
        for ($i = 1; $i < count($rows); $i++) {
            // 檢查該行是否有資料（避免更新空白行）
            if (!isset($rows[$i][1]) || trim($rows[$i][1]) === '') {
                continue;
            }
            
            // 新的ID應該是當前行索引
            $newId = $i;
            
            // 準備更新資料（N欄，索引13）
            $updateData[] = [
                'range' => sprintf('%s!N%d', $sheetName, $i + 1), // N欄，行號+1
                'values' => [[$newId]]
            ];
            
            $updatedCount++;
        }
        
        // 如果有資料需要更新，執行批量更新
        if (!empty($updateData)) {
            $batchUpdateRequest = new Google_Service_Sheets_BatchUpdateValuesRequest([
                'valueInputOption' => 'USER_ENTERED',
                'data' => $updateData
            ]);
            
            $service->spreadsheets_values->batchUpdate($spreadsheetId, $batchUpdateRequest);
        }
        
        return [
            'success' => true,
            'message' => "成功重新排序 {$updatedCount} 個訂單的ID",
            'updated_rows' => $updatedCount
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '重新排序ID時發生錯誤：' . $e->getMessage(),
            'updated_rows' => 0
        ];
    }
}

// 執行測試
if (php_sapi_name() === 'cli') {
    // 命令列執行
    testIdReorder();
} else {
    // 網頁執行
    header('Content-Type: text/plain; charset=utf-8');
    testIdReorder();
}
?>
