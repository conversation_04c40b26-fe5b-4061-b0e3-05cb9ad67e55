<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 路徑測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API 路徑測試工具</h1>

        <div class="test-result info">
            <strong>當前環境資訊：</strong><br>
            <span id="env-info"></span>
        </div>

        <div class="test-result info">
            <strong>計算出的 API 路徑：</strong><br>
            <span id="api-path"></span>
        </div>

        <h2>測試按鈕</h2>
        <button onclick="testGetOrders()">測試獲取訂單 API</button>
        <button onclick="testUpdateItems()">測試更新商品 API</button>
        <button onclick="testGetCustomers()">測試獲取客戶 API</button>
        <button onclick="clearResults()">清除結果</button>

        <h2>測試結果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 複製 API 路徑邏輯
        const getApiBase = () => {
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const port = window.location.port;

            // 本地開發環境（localhost:8080 指向 htdocs，需要完整路徑）
            if (isLocalhost && port === '8080') {
                return '/sheet-order-dashboard-main/api';
            }

            // Cloudflare Tunnels 環境（node.767780.xyz 直接指向 sheet-order-dashboard-main 目錄）
            // 所以 API 路徑就是 /api
            return '/api';
        };

        const API_BASE = getApiBase();

        // 顯示環境資訊
        function displayEnvInfo() {
            const envInfo = `
主機名稱: ${window.location.hostname}
端口: ${window.location.port || '預設'}
協議: ${window.location.protocol}
完整 URL: ${window.location.href}
是否本地主機: ${window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'}
            `;
            document.getElementById('env-info').textContent = envInfo;
            document.getElementById('api-path').textContent = API_BASE;
        }

        // 添加測試結果
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong><br>${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 測試獲取訂單 API
        async function testGetOrders() {
            addResult('🔄 開始測試獲取訂單 API...', 'info');

            try {
                const timestamp = Date.now();
                const nonce = Math.random().toString(36).substring(2, 15);

                const url = new URL(`${window.location.origin}${API_BASE}/get_orders_from_sheet.php`);
                url.searchParams.append('_', timestamp.toString());
                url.searchParams.append('nonce', nonce);

                addResult(`請求 URL: ${url.toString()}`, 'info');

                const response = await fetch(url.toString(), {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                addResult(`HTTP 狀態: ${response.status} ${response.statusText}`,
                    response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 獲取訂單 API 測試成功！\n回應資料: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ 獲取訂單 API 測試失敗！\n錯誤內容: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 獲取訂單 API 測試發生錯誤: ${error.message}`, 'error');
            }
        }

        // 測試更新商品 API
        async function testUpdateItems() {
            addResult('🔄 開始測試更新商品 API...', 'info');

            try {
                const timestamp = Date.now();
                const nonce = Math.random().toString(36).substring(2, 15);

                const url = new URL(`${window.location.origin}${API_BASE}/update_order_items.php`);
                url.searchParams.append('_', timestamp.toString());
                url.searchParams.append('nonce', nonce);

                const testData = {
                    id: '1',
                    items: [
                        {
                            product: '原味蘿蔔糕',
                            quantity: 1,
                            price: 250,
                            subtotal: 250
                        }
                    ],
                    total: 250,
                    timestamp: timestamp,
                    nonce: nonce
                };

                addResult(`請求 URL: ${url.toString()}`, 'info');
                addResult(`請求資料: ${JSON.stringify(testData, null, 2)}`, 'info');

                const response = await fetch(url.toString(), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    body: JSON.stringify(testData)
                });

                addResult(`HTTP 狀態: ${response.status} ${response.statusText}`,
                    response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 更新商品 API 測試成功！\n回應資料: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ 更新商品 API 測試失敗！\n錯誤內容: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 更新商品 API 測試發生錯誤: ${error.message}`, 'error');
            }
        }

        // 測試獲取客戶 API
        async function testGetCustomers() {
            addResult('🔄 開始測試獲取客戶 API...', 'info');

            try {
                const timestamp = Date.now();
                const nonce = Math.random().toString(36).substring(2, 15);

                const url = new URL(`${window.location.origin}${API_BASE}/get_customers_from_sheet.php`);
                url.searchParams.append('_', timestamp.toString());
                url.searchParams.append('nonce', nonce);

                addResult(`請求 URL: ${url.toString()}`, 'info');

                const response = await fetch(url.toString(), {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                addResult(`HTTP 狀態: ${response.status} ${response.statusText}`,
                    response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 獲取客戶 API 測試成功！\n回應資料: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ 獲取客戶 API 測試失敗！\n錯誤內容: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 獲取客戶 API 測試發生錯誤: ${error.message}`, 'error');
            }
        }

        // 清除結果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // 頁面載入時顯示環境資訊
        window.onload = function() {
            displayEnvInfo();
            addResult('🚀 API 路徑測試工具已準備就緒！', 'success');
        };
    </script>
</body>
</html>
