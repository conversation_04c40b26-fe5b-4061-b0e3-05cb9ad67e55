import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import Dashboard from '@/components/Dashboard';
import CustomerDashboard from '@/components/CustomerDashboard';
import OrderFilters, { OrderFiltersRef } from '@/components/OrderFilters';
import CustomerFilters from '@/components/CustomerFilters';
import OrderList from '@/components/OrderList';
import CustomerList from '@/components/CustomerList';
import OrderDetail from '@/components/OrderDetail';
import CustomerDetail from '@/components/CustomerDetail';
import CompactControlPanel from '@/components/CompactControlPanel';
import ModernSidebar from '@/components/ModernSidebar';
import ScrollToTopButton from '@/components/ScrollToTopButton';
import { Order, PaymentStatus } from '@/types/order';
import { CustomerWithStats } from '../types/customer';
import { FilterCriteria } from '../types/filters';
import { CustomerFilterCriteria } from '../types/customer';
import { fetchOrders } from '@/services/orderService';
import { fetchCustomers, getCustomerStats } from '@/services/customerService';
import { downloadExcelCsv, printOrders } from '@/utils/exportUtils';
import { downloadQuickStoreXlsx } from '@/utils/exportQuickStoreXlsx';
import { Download, Printer, Users, ShoppingBag, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Index: React.FC = () => {
  // 檢測是否在 iframe 中
  const [isInIframe, setIsInIframe] = useState(false);

  // 頁面模式：'orders' 或 'customers'
  const [pageMode, setPageMode] = useState<'orders' | 'customers'>('orders');

  // 訂單相關狀態
  // 已選擇訂單 id 陣列
  const [selected, setSelected] = useState<string[]>([]);
  // 已選擇訂單 id 變動 callback
  const handleSelectedChange = (ids: string[]) => setSelected(ids);

  // 客戶相關狀態
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerWithStats | null>(null);
  const [isCustomerDetailOpen, setIsCustomerDetailOpen] = useState(false);
  const [customerFilters, setCustomerFilters] = useState<CustomerFilterCriteria>({
    region: '',
    purchasedItem: '',
    search: ''
  });
  const [customerDashboardRefreshTrigger, setCustomerDashboardRefreshTrigger] = useState(0);

  // 客戶 id 變動 callback
  const handleSelectedCustomersChange = (ids: string[]) => setSelectedCustomers(ids);

  // 下載快速到店 xlsx
  const handleDownloadQuickStoreXlsx = async () => {
    try {
      const allOrders = await fetchOrders();
      const selectedOrders = allOrders.filter(order => selected.includes(order.id));
      if (selectedOrders.length === 0) {
        toast({
          title: '提示',
          description: '請勾選要匯出的訂單',
        });
        return;
      }
      downloadQuickStoreXlsx(selectedOrders, `快速到店訂單_${new Date().toISOString().split('T')[0]}.xlsx`);
      toast({
        title: '成功',
        description: '快速到店 xlsx 已下載',
      });
    } catch (error) {
      console.error('Failed to download QuickStore xlsx:', error);
      toast({
        title: '錯誤',
        description: '下載快速到店 xlsx 失敗',
        variant: 'destructive',
      });
    }
  };

  // 列印已選擇訂單
  const handlePrintSelected = async () => {
    try {
      const allOrders = await fetchOrders(filters);
      const selectedOrders = allOrders.filter(order => selected.includes(order.id));
      if (selectedOrders.length === 0) {
        toast({ title: '提示', description: '請選擇要列印的訂單' });
        return;
      }
      printOrders(selectedOrders);
    } catch (error) {
      toast({ title: '錯誤', description: '列印失敗', variant: 'destructive' });
    }
  };

  const [filters, setFilters] = useState<FilterCriteria>({
    status: '',
    deliveryMethod: '',
    paymentStatus: '',
    search: ''
  });
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [orderListRef, setOrderListRef] = useState<any>(null);
  const [stats, setStats] = useState({
    total: 0,
    processing: 0,
    selected: 0
  });
  const [customerStats, setCustomerStats] = useState({
    total: 0,
    active: 0,
    filteredTotal: 0
  });
  const [dashboardRefreshTrigger, setDashboardRefreshTrigger] = useState(0);
  const { toast } = useToast();

  // 篩選器 ref，用於重置功能
  const orderFiltersRef = useRef<OrderFiltersRef>(null);

  // 開啟到貨日設定頁面
  const openDeliverySettings = () => {
    window.open('http://767780.xyz/project-proj_1iZQ/admin-delivery-settings.php', '_blank');
  };

  useEffect(() => {
    updateStats();
    updateCustomerStats();

    // 檢測是否在 iframe 中
    setIsInIframe(window.self !== window.top);
  }, []);

  const updateStats = async () => {
    try {
      const allOrders = await fetchOrders();
      setStats({
        total: allOrders.length,
        processing: allOrders.filter(order => order.status === '已抄單').length,
        selected: 0
      });
    } catch (error) {
      console.error('Failed to update stats:', error);
    }
  };

  const updateCustomerStats = async () => {
    try {
      const allCustomers = await fetchCustomers();
      const stats = getCustomerStats(allCustomers);
      setCustomerStats({
        total: stats.total,
        active: allCustomers.filter(customer => customer.purchaseCount > 0).length,
        filteredTotal: stats.total
      });
    } catch (error) {
      console.error('Failed to update customer stats:', error);
    }
  };

  const handleFilterChange = (newFilters: FilterCriteria) => {
    setFilters(newFilters);
  };

  const handleOrderClick = (
    order: Order,
    updateOrderInList: (
      orderId: string,
      newStatus?: '訂單確認中' | '已抄單' | '已出貨' | '取消訂單',
      newPaymentStatus?: PaymentStatus
    ) => void
  ) => {
    setSelectedOrder({ ...order });
    setIsDetailOpen(true);
    setOrderListRef(() => updateOrderInList);
  };

  const handleCloseDetail = () => {
    setIsDetailOpen(false);
    setSelectedOrder(null);
  };

  const handleOrdersChange = () => {
    updateStats();
    // 觸發 Dashboard 重新載入
    setDashboardRefreshTrigger(prev => prev + 1);
  };

  // 客戶相關處理函數
  const handleCustomerFilterChange = (newFilters: Partial<CustomerFilterCriteria>) => {
    setCustomerFilters(prev => ({ ...prev, ...newFilters }));
    // 當篩選條件改變時，更新客戶統計
    updateCustomerStatsWithFilters({ ...customerFilters, ...newFilters });
  };

  const handleCustomerClick = (customer: CustomerWithStats) => {
    setSelectedCustomer(customer);
    setIsCustomerDetailOpen(true);
  };

  const handleCloseCustomerDetail = () => {
    setIsCustomerDetailOpen(false);
    setSelectedCustomer(null);
  };

  const handleCustomersChange = () => {
    // 觸發 CustomerDashboard 重新載入
    setCustomerDashboardRefreshTrigger(prev => prev + 1);
    // 更新客戶統計
    updateCustomerStats();
  };

  // 根據篩選條件更新客戶統計
  const updateCustomerStatsWithFilters = async (filters: CustomerFilterCriteria) => {
    try {
      const filteredCustomers = await fetchCustomers(filters);
      setCustomerStats(prev => ({
        ...prev,
        filteredTotal: filteredCustomers.length
      }));
    } catch (error) {
      console.error('Failed to update filtered customer stats:', error);
    }
  };

  // 處理客戶總數變化
  const handleCustomerTotalCountChange = (total: number) => {
    setCustomerStats(prev => ({
      ...prev,
      filteredTotal: total
    }));
  };

  // 重置訂單篩選器
  const handleResetOrderFilters = () => {
    if (orderFiltersRef.current) {
      orderFiltersRef.current.resetFilters();
    }
  };

  const handleDownloadCsv = async () => {
    try {
      const allOrders = await fetchOrders();
      const selectedOrders = allOrders.filter(order => selected.includes(order.id));
      if (selectedOrders.length === 0) {
        toast({
          title: '提示',
          description: '請勾選要匯出的訂單',
        });
        return;
      }
      // 使用Excel專用的CSV下載功能，解決中文亂碼問題
      downloadExcelCsv(selectedOrders, `訂單資料_${new Date().toISOString().split('T')[0]}.csv`);
      toast({
        title: '成功',
        description: 'CSV檔案已下載（Unicode UTF-8編碼）',
      });
    } catch (error) {
      console.error('Failed to download CSV:', error);
      toast({
        title: '錯誤',
        description: '下載CSV檔案失敗',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* 側邊欄 - 只在非 iframe 模式下顯示 */}
      {!isInIframe && (
        <ModernSidebar
          pageMode={pageMode}
          onPageModeChange={setPageMode}
          orderStats={{
            total: stats.total,
            pending: stats.processing,
            completed: stats.selected
          }}
          customerStats={{
            total: customerStats.total,
            active: customerStats.active
          }}
        />
      )}

      {/* 主內容區域 */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* 只在非 iframe 模式下顯示頂部工具欄 */}
        {!isInIframe && (
          <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-30">
            <div className="px-6 py-3 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <h1 className="text-xl font-bold text-foreground">
                  {pageMode === 'orders' ? '訂單管理' : '客戶資料'}
                </h1>
                <div className="text-sm text-muted-foreground">
                  蘿蔔糕訂單系統 - 管理後台
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={openDeliverySettings}
                  className="h-8 px-3 text-xs border-2 border-purple-400 text-purple-600 hover:bg-purple-50 hover:border-purple-500 transition-all font-medium"
                >
                  <Calendar className="h-3 w-3 mr-1" />
                  設定到貨日期
                </Button>
              </div>
            </div>
          </header>
        )}

      {/* iframe 模式下顯示簡化的導航 */}
      {isInIframe && (
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant={pageMode === 'orders' ? 'default' : 'outline'}
                size="sm"
                className={`${pageMode === 'orders' ? 'bg-primary text-white' : ''}`}
                onClick={() => setPageMode('orders')}
              >
                <ShoppingBag className="h-3 w-3 mr-1" /> 訂單
              </Button>
              <Button
                variant={pageMode === 'customers' ? 'default' : 'outline'}
                size="sm"
                className={pageMode === 'customers' ? 'bg-primary text-white' : ''}
                onClick={() => setPageMode('customers')}
              >
                <Users className="h-3 w-3 mr-1" /> 客戶
              </Button>
            </div>
          </div>
        </div>
      )}

        <main className={`flex-1 ${isInIframe ? 'p-3' : 'p-6'}`}>

        {/* 訂單頁面 */}
        {pageMode === 'orders' && (
          <>
            {/* 整合式控制面板 */}
            <CompactControlPanel
              statsComponent={<Dashboard refreshTrigger={dashboardRefreshTrigger} compact={true} />}
              filtersComponent={<OrderFilters ref={orderFiltersRef} onFilterChange={handleFilterChange} />}
              onResetFilters={handleResetOrderFilters}
              actionButtons={
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 px-3 text-xs border-2 border-orange-400 text-orange-600 hover:bg-orange-50 hover:border-orange-500 transition-all font-medium"
                    onClick={handlePrintSelected}
                  >
                    <Printer className="h-3 w-3 mr-1" /> 列印訂單
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 px-3 text-xs border-2 border-blue-400 text-blue-600 hover:bg-blue-50 hover:border-blue-500 transition-all font-medium"
                    onClick={handleDownloadCsv}
                  >
                    <Download className="h-3 w-3 mr-1" /> 宅配CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 px-3 text-xs border-2 border-green-400 text-green-600 hover:bg-green-50 hover:border-green-500 transition-all font-medium"
                    onClick={handleDownloadQuickStoreXlsx}
                  >
                    <Download className="h-3 w-3 mr-1" /> 快速到店
                  </Button>
                </>
              }
              totalItems={stats.total}
              selectedCount={selected.length}
              itemType="訂單"
              defaultExpanded={false}
            />

            <OrderList
              filters={filters}
              onOrderClick={handleOrderClick}
              onOrdersChange={handleOrdersChange}
              selected={selected}
              onSelectedChange={handleSelectedChange}
            />

            <OrderDetail
              order={selectedOrder}
              open={isDetailOpen}
              onClose={handleCloseDetail}
              onOrderStatusUpdate={(orderId, newStatus, newPaymentStatus) => {
                if (orderListRef) orderListRef(orderId, newStatus, newPaymentStatus);
                handleOrdersChange();
              }}
            />
          </>
        )}

        {/* 客戶頁面 */}
        {pageMode === 'customers' && (
          <>
            {/* 客戶整合式控制面板 */}
            <CompactControlPanel
              statsComponent={<CustomerDashboard refreshTrigger={customerDashboardRefreshTrigger} compact={true} />}
              filtersComponent={<CustomerFilters onFilterChange={handleCustomerFilterChange} />}
              actionButtons={
                <>
                  {/* 客戶相關的操作按鈕可以在這裡添加 */}
                </>
              }
              totalItems={customerStats.filteredTotal}
              selectedCount={selectedCustomers.length}
              itemType="客戶"
              defaultExpanded={false}
            />

            <CustomerList
              filters={customerFilters}
              onCustomerClick={handleCustomerClick}
              onCustomersChange={handleCustomersChange}
              selected={selectedCustomers}
              onSelectedChange={handleSelectedCustomersChange}
              onTotalCountChange={handleCustomerTotalCountChange}
            />

            <CustomerDetail
              customer={selectedCustomer}
              open={isCustomerDetailOpen}
              onClose={handleCloseCustomerDetail}
            />
          </>
        )}
        </main>
      </div>

      {/* 回頂端浮動按鈕 */}
      <ScrollToTopButton threshold={40} />
    </div>
  );
};

export default Index;