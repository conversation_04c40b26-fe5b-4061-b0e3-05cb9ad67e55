import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
/*
if (import.meta.env.MODE === 'development') {
  import('@stagewise/toolbar-react').then(({ StagewiseToolbar }) => {
    const config = { plugins: [] };
    const toolbarRoot = document.createElement('div');
    toolbarRoot.id = 'stagewise-toolbar-root';
    document.body.appendChild(toolbarRoot);
    import('react-dom/client').then(({ createRoot }) => {
      createRoot(toolbarRoot).render(<StagewiseToolbar config={config} />);
    });
  });
}
*/
createRoot(document.getElementById("root")!).render(<App />);
