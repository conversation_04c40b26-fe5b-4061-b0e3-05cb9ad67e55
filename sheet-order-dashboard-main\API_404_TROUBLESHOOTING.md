# API 404 錯誤診斷指南

## 🚨 問題現象

測試結果顯示：
- 請求 URL: `https://node.767780.xyz/api/update_order_items.php`
- HTTP 狀態: **404 Not Found**
- 錯誤訊息: "The requested URL was not found on this server"

## 🔍 可能原因分析

### 1. .htaccess 重寫規則問題
**已修正**：簡化了 .htaccess 檔案，移除可能造成衝突的重寫規則

### 2. 檔案路徑問題
**檢查結果**：檔案確實存在於 `sheet-order-dashboard-main/api/update_order_items.php`

### 3. Apache 配置問題
可能的問題：
- mod_rewrite 未啟用
- 檔案權限問題
- PHP 處理器配置問題

## 🛠️ 診斷步驟

### 步驟 1：測試基本 API 訪問
訪問測試端點：
```
https://node.767780.xyz/api/test_api_access.php
```

**預期結果**：應該返回 JSON 格式的成功訊息

### 步驟 2：檢查檔案權限
確保以下檔案具有適當權限：
- `api/` 目錄：755
- `api/*.php` 檔案：644

### 步驟 3：檢查 Apache 模組
確認以下模組已啟用：
- mod_rewrite
- mod_headers
- mod_php (或 php-fpm)

### 步驟 4：檢查 PHP 配置
確認 PHP 可以正常處理 .php 檔案

## 🔧 修復方案

### 方案 1：簡化 .htaccess（已完成）
```apache
# 啟用 RewriteEngine
RewriteEngine On

# 設置基礎路徑
RewriteBase /

# 如果請求的是實際存在的文件或目錄，則不進行重寫
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# 處理前端路由（除了 API 請求）
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^ index.html [L]
```

### 方案 2：檢查 XAMPP 配置
確認 XAMPP 的 Apache 配置：

1. **檢查 httpd.conf**：
   ```apache
   LoadModule rewrite_module modules/mod_rewrite.so
   LoadModule headers_module modules/mod_headers.so
   ```

2. **檢查虛擬主機配置**：
   ```apache
   <VirtualHost *:8080>
       DocumentRoot "D:/xampp/htdocs/sheet-order-dashboard-main"
       ServerName localhost
       
       <Directory "D:/xampp/htdocs/sheet-order-dashboard-main">
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

### 方案 3：直接測試 PHP 檔案
如果 .htaccess 有問題，可以暫時重新命名或刪除 .htaccess 檔案，然後直接訪問：
```
https://node.767780.xyz/api/update_order_items.php
```

### 方案 4：檢查 Cloudflare 設定
確認 Cloudflare Tunnels 設定：
- 確保指向正確的本地端口 (8080)
- 檢查是否有快取或代理設定影響

## 🧪 測試清單

### ✅ 基本測試
- [ ] 訪問 `https://node.767780.xyz/api/test_api_access.php`
- [ ] 檢查返回的 JSON 資料
- [ ] 確認 `file_exists_check` 中所有檔案都是 `true`

### ✅ 權限測試
- [ ] 檢查 `api/` 目錄權限
- [ ] 檢查 PHP 檔案權限
- [ ] 確認 Apache 可以讀取檔案

### ✅ Apache 測試
- [ ] 檢查 Apache 錯誤日誌
- [ ] 確認 mod_rewrite 已啟用
- [ ] 測試 .htaccess 規則

### ✅ PHP 測試
- [ ] 確認 PHP 版本兼容
- [ ] 檢查 PHP 錯誤日誌
- [ ] 測試基本 PHP 執行

## 📋 快速修復指令

### Windows/XAMPP 環境
```powershell
# 檢查檔案是否存在
Test-Path "D:\xampp\htdocs\sheet-order-dashboard-main\api\update_order_items.php"

# 檢查 Apache 是否運行
Get-Process | Where-Object {$_.ProcessName -eq "httpd"}

# 重啟 Apache（如果需要）
# 在 XAMPP Control Panel 中重啟 Apache
```

### 檢查 Apache 日誌
查看以下日誌檔案：
- `D:\xampp\apache\logs\error.log`
- `D:\xampp\apache\logs\access.log`

## 🎯 預期解決方案

根據測試結果，最可能的解決方案是：

1. **測試基本 API 訪問**：先確認 `test_api_access.php` 是否可以正常訪問
2. **檢查 Apache 配置**：確認 mod_rewrite 和相關模組已啟用
3. **簡化 .htaccess**：已完成，移除了可能造成衝突的規則
4. **檢查檔案權限**：確保所有檔案都有適當的讀取權限

## 📞 下一步行動

1. 首先測試：`https://node.767780.xyz/api/test_api_access.php`
2. 根據測試結果進行相應的修復
3. 如果基本測試通過，再測試 `update_order_items.php`
4. 檢查 Apache 和 PHP 日誌以獲取更多資訊

---

**注意**：如果問題持續存在，請提供 `test_api_access.php` 的測試結果，以便進一步診斷問題。
