---
description: 融氏古早味手工蘿蔔糕網站專案結構與功能
---

專案是一個融氏古早味手工蘿蔔糕的網站，包含線上訂購系統和管理後台。

主要功能：
1. 線上訂購系統 - 客戶可以在網站上直接訂購產品
2. 管理後台 - 管理員可以查看和管理訂單
3. 響應式設計 - 適配各種設備尺寸

技術棧：
- 前端：React, JavaScript, HTML, CSS, Tailwind CSS
- 後端：PHP, MariaDB
- 伺服器：XAMPP (Apache, MySQL)

產品項目：
- 原味蘿蔔糕 ($250/盒)
- 極濃芋頭糕 ($350/盒)
- 港式蘿蔔糕 ($350/盒)

運費計算：
- 訂單總額低於 $350 時，收取 $100 運費
- 訂單總額 $350 或以上時，免運費

專案結構：
lobon/
├── api/                # 後端 API 接口
│   ├── db.php         # 資料庫連接和查詢處理
│   ├── reset_admin.php # 重置管理員帳號
│   └── error.log      # 錯誤日誌
├── components/         # 前端元件
│   ├── OrderForm.js   # 訂單表單元件
│   └── admin/         # 管理後台元件
│       └── Login.js   # 管理員登入元件
├── admin.html         # 管理後台頁面
└── pos8.php           # 原始訂單系統參考