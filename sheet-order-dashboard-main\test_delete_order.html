<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試刪除訂單功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input {
            width: 200px;
        }
        button {
            background-color: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background-color: #c82333;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .orders-list {
            margin-top: 20px;
        }
        .order-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ 測試刪除訂單功能</h1>

        <div class="warning">
            <strong>⚠️ 警告：</strong>此測試會真正刪除 Google Sheets 中的訂單資料，請謹慎使用！
        </div>

        <div class="form-group">
            <button onclick="loadOrders()" id="loadBtn">載入訂單列表</button>
        </div>

        <div id="ordersList" class="orders-list"></div>

        <div class="form-group">
            <label for="orderId">要刪除的訂單 ID（行索引）：</label>
            <input type="number" id="orderId" placeholder="例如：2" min="1">
            <button onclick="deleteOrder()" id="deleteBtn">刪除訂單</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        const API_BASE = '/sheet-order-dashboard-main/api';

        async function loadOrders() {
            const loadBtn = document.getElementById('loadBtn');
            const ordersList = document.getElementById('ordersList');

            loadBtn.disabled = true;
            loadBtn.textContent = '載入中...';

            try {
                const timestamp = Date.now();
                const nonce = Math.random().toString(36).substring(2, 15);
                const url = `${API_BASE}/get_orders_from_sheet.php?refresh=1&_=${timestamp}&nonce=${nonce}`;

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                const result = await response.json();

                if (result.success && result.data) {
                    ordersList.innerHTML = '<h3>📋 現有訂單列表：</h3>';
                    result.data.forEach((order, index) => {
                        const orderDiv = document.createElement('div');
                        orderDiv.className = 'order-item';
                        orderDiv.innerHTML = `
                            <strong>ID: ${order.id}</strong> |
                            客戶: ${order.customerName} |
                            電話: ${order.customerPhone} |
                            商品: ${order.items} |
                            金額: $${order.amount}
                        `;
                        ordersList.appendChild(orderDiv);
                    });
                } else {
                    ordersList.innerHTML = '<p>無法載入訂單或沒有訂單資料</p>';
                }
            } catch (error) {
                ordersList.innerHTML = `<p class="error">載入失敗：${error.message}</p>`;
            } finally {
                loadBtn.disabled = false;
                loadBtn.textContent = '載入訂單列表';
            }
        }

        async function deleteOrder() {
            const orderId = document.getElementById('orderId').value;
            const deleteBtn = document.getElementById('deleteBtn');
            const resultDiv = document.getElementById('result');

            if (!orderId) {
                alert('請輸入要刪除的訂單 ID');
                return;
            }

            if (!confirm(`確定要刪除訂單 ID ${orderId} 嗎？\n\n⚠️ 此操作將從 Google Sheets 中永久刪除該訂單，無法復原！`)) {
                return;
            }

            deleteBtn.disabled = true;
            deleteBtn.textContent = '刪除中...';
            resultDiv.innerHTML = '';

            try {
                const timestamp = Date.now();
                const nonce = Math.random().toString(36).substring(2, 15);
                const url = `${API_BASE}/delete_order.php?_=${timestamp}&nonce=${nonce}`;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    body: JSON.stringify({
                        id: orderId,
                        timestamp: timestamp,
                        nonce: nonce
                    })
                });

                const result = await response.json();

                if (result.success) {
                    let message = `✅ 刪除成功！\n\n`;
                    message += `訂單已從 Google Sheets 中永久刪除\n`;
                    message += `刪除的行號: ${result.deleted_row}\n\n`;

                    if (result.reorder_result) {
                        message += `📋 ID重排序結果:\n`;
                        message += `- 狀態: ${result.reorder_result.success ? '成功' : '失敗'}\n`;
                        message += `- 訊息: ${result.reorder_result.message}\n`;
                        message += `- 更新行數: ${result.reorder_result.updated_rows}\n`;
                        if (result.reorder_result.start_index !== undefined) {
                            message += `- 開始索引: ${result.reorder_result.start_index}\n`;
                        }
                        if (result.reorder_result.total_rows !== undefined) {
                            message += `- 總行數: ${result.reorder_result.total_rows}\n`;
                        }
                    }

                    message += `\n完整回應:\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.innerHTML = `<div class="result success">${message}</div>`;

                    // 自動重新載入訂單列表
                    setTimeout(() => {
                        loadOrders();
                    }, 1000);
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 失敗！\n\n${JSON.stringify(result, null, 2)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 網路錯誤：${error.message}</div>`;
            } finally {
                deleteBtn.disabled = false;
                deleteBtn.textContent = '刪除訂單';
            }
        }

        // 頁面載入時自動載入訂單列表
        window.onload = function() {
            loadOrders();
        };
    </script>
</body>
</html>
