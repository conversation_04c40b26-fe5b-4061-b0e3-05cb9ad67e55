# 訂單商品編輯功能使用指南

## 🎯 功能概述

新增的訂單商品編輯功能允許管理員直接在訂單詳情頁面編輯商品數量、新增商品、刪除商品，並自動重新計算總金額，同時同步到 Google Sheets。

## 🚀 功能特色

### ✨ 主要功能
- **商品數量調整**：使用 +/- 按鈕或直接輸入數字調整商品數量
- **新增商品**：從下拉選單選擇商品並新增到訂單中
- **刪除商品**：移除不需要的商品項目
- **即時金額計算**：自動重新計算訂單總金額
- **Google Sheets 同步**：所有變更即時同步到 Google Sheets

### 🛍️ 可用商品清單
- **原味蘿蔔糕**：$250
- **芋頭粿**：$350
- **港式蘿蔔糕**：$350

## 📱 使用方法

### 1. 開啟訂單詳情
1. 在訂單列表中點擊任一訂單
2. 訂單詳情對話框會開啟

### 2. 編輯商品
1. 在「訂購商品」區域點擊「編輯商品」按鈕
2. 商品編輯對話框會開啟

### 3. 調整商品數量
- **使用按鈕**：點擊 `-` 或 `+` 按鈕調整數量
- **直接輸入**：在數量輸入框中直接輸入數字
- **最小數量**：每個商品最少需要 1 個

### 4. 新增商品
1. 在「新增商品」區域選擇商品
2. 點擊「新增」按鈕
3. 如果商品已存在，會自動增加數量

### 5. 刪除商品
- 點擊商品項目右側的垃圾桶圖示

### 6. 儲存變更
1. 確認所有變更無誤
2. 點擊「儲存變更」按鈕
3. 系統會自動同步到 Google Sheets

## 🔧 技術實現

### 前端組件
- **OrderItemEditor.tsx**：商品編輯對話框
- **OrderDetail.tsx**：整合編輯功能的訂單詳情頁面

### 後端API
- **update_order_items.php**：處理商品更新的 API 端點

### 資料流程
1. 前端收集編輯後的商品資料
2. 計算新的總金額
3. 發送 API 請求到後端
4. 後端驗證資料並更新 Google Sheets
5. 清除快取確保資料一致性
6. 前端更新顯示

## 🛡️ 安全性與驗證

### 前端驗證
- 商品數量必須為正整數
- 訂單不能為空（至少需要一個商品）
- 商品必須從預定義清單中選擇

### 後端驗證
- 參數格式驗證
- 商品資料結構驗證
- Google Sheets 連接驗證

## 🔄 快取機制

- 編輯成功後自動清除訂單快取
- 確保下次讀取時獲取最新資料
- 避免資料不一致問題

## 🎨 用戶體驗

### 即時反饋
- 數量變更即時顯示新的小計
- 總金額即時更新
- 操作成功/失敗的 Toast 提示

### 響應式設計
- 支援桌面和行動裝置
- 對話框可滾動，適應不同螢幕尺寸

## 🐛 錯誤處理

### 常見錯誤情況
- 網路連接問題
- Google Sheets API 錯誤
- 資料格式錯誤
- 權限問題

### 錯誤提示
- 清楚的錯誤訊息
- 建議的解決方案
- 自動重試機制

## 📊 資料格式

### Google Sheets 更新格式
- **items 欄位**：`"原味蘿蔔糕 x 2, 芋頭粿 x 1"`
- **amount 欄位**：數字格式的總金額

### API 請求格式
```json
{
  "id": "訂單ID",
  "items": [
    {
      "product": "原味蘿蔔糕",
      "quantity": 2,
      "price": 250,
      "subtotal": 500
    }
  ],
  "total": 500
}
```

## 🔮 未來擴展

### 可能的改進
- 支援自定義商品價格
- 批次編輯多個訂單
- 商品庫存管理
- 編輯歷史記錄
- 更多商品類型支援

---

**注意**：此功能需要確保 Google Sheets API 正常運作，且具備適當的寫入權限。
