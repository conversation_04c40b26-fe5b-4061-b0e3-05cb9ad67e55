# 刪除訂單功能升級說明

## 概述

本次升級將原本的「清空訂單內容」功能改為「真正刪除 Google Sheets 中的訂單行」，並新增「ID重排序功能」，提供更徹底且智能的刪除體驗。

## 修改內容

### 1. 後端 API 修改 (`api/delete_order.php`)

#### 原始功能
- 使用 `clear` 方法清空指定行的內容
- 保留空白行結構
- 不影響其他行的位置

#### 新功能
- 使用 `batchUpdate` 方法的 `deleteDimension` 操作
- 真正刪除整行
- 後續行自動向上移動
- 獲取工作表 ID 以支援 `deleteDimension` 操作
- **新增ID重排序功能**：自動重新排序後續訂單的ID，確保ID連續性

#### 主要變更
```php
// 舊方法：清空內容
$clearRange = sprintf("%s!A%d:%s%d", $sheetName, $targetRow, $lastColLetter, $targetRow);
$body = new Google_Service_Sheets_ClearValuesRequest();
$service->spreadsheets_values->clear($spreadsheetId, $clearRange, $body);

// 新方法：刪除整行
$requests = [
    [
        'deleteDimension' => [
            'range' => [
                'sheetId' => $sheetId,
                'dimension' => 'ROWS',
                'startIndex' => $targetRowIndex,
                'endIndex' => $targetRowIndex + 1
            ]
        ]
    ]
];
$batchUpdateRequest = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest(['requests' => $requests]);
$response = $service->spreadsheets->batchUpdate($spreadsheetId, $batchUpdateRequest);

// 新增：重新排序後續訂單的ID
$reorderResult = reorderOrderIds($service, $spreadsheetId, $sheetName, $targetRowIndex);
```

### 2. 前端確認對話框升級 (`src/components/OrderList.tsx`)

#### 新增功能
- 顯示詳細的確認對話框
- 明確警告此操作不可復原
- 顯示要刪除的訂單編號
- 更詳細的成功/失敗訊息

#### 主要變更
```typescript
// 新增確認對話框
const confirmed = window.confirm(
  `確定要刪除訂單 ${orderNumber} 嗎？\n\n⚠️ 注意：此操作將會從 Google Sheets 中永久刪除該訂單資料，無法復原！`
);

// 更詳細的成功訊息
toast({
  title: '成功',
  description: `訂單 ${orderNumber} 已從 Google Sheets 中永久刪除`,
});
```

### 3. 資料讀取邏輯優化 (`api/get_orders_from_sheet.php`)

#### 改進內容
- 更清晰的註解說明 ID 生成邏輯
- 格式化的訂單編號生成
- 更好的空白行處理

#### 主要變更
```php
// 使用行索引作為 ID，這樣刪除功能可以正確定位要刪除的行
// 注意：刪除後行號會改變，但這是預期的行為
$orders[] = [
    'createdAt' => $row[0] ?? '',
    'id' => $idx, // 使用當前行索引作為 ID
    'orderNumber' => sprintf('ORD-%03d', $idx), // 生成格式化的訂單編號
    // ... 其他欄位
];
```

### 4. API 文檔更新 (`API_DOCUMENTATION.md`)

#### 新增內容
- 重要警告提醒
- 詳細的功能說明
- 注意事項說明
- 錯誤處理範例

### 5. ID重排序功能 (`reorderOrderIds` 函數)

#### 功能說明
- **自動觸發**：在刪除訂單後自動執行
- **智能更新**：只更新被刪除行之後的訂單ID
- **批量處理**：使用 `batchUpdate` 一次性更新多個ID
- **錯誤處理**：完整的錯誤處理和狀態回報

#### 實現邏輯
```php
function reorderOrderIds($service, $spreadsheetId, $sheetName, $deletedRowIndex) {
    // 1. 重新獲取所有資料
    // 2. 檢查是否有需要更新的行
    // 3. 準備批量更新的資料（更新N欄的ID）
    // 4. 執行批量更新
    // 5. 返回更新結果
}
```

#### 更新範圍
- **目標欄位**：Google Sheets 的 N 欄（第14個欄位，索引13）
- **更新邏輯**：新的ID = 當前行索引
- **批量更新**：一次性更新所有需要調整的ID

### 6. 測試工具 (`test_delete_order.html`)

#### 功能特色
- 視覺化的訂單列表顯示
- 安全的確認機制
- 即時的結果反饋
- 自動重新載入功能
- **新增**：顯示ID重排序結果

## 使用方式

### 1. 透過後台介面
1. 登入後台管理系統
2. 在訂單列表中找到要刪除的訂單
3. 點擊刪除按鈕（垃圾桶圖示）
4. 確認刪除對話框
5. 系統自動重新載入最新資料

### 2. 透過測試工具
1. 開啟 `test_delete_order.html`
2. 點擊「載入訂單列表」查看現有訂單
3. 輸入要刪除的訂單 ID
4. 點擊「刪除訂單」並確認
5. 查看結果並自動重新載入

## 注意事項

### ⚠️ 重要警告
1. **不可復原**：刪除操作會永久移除 Google Sheets 中的資料
2. **ID自動重排序**：刪除後會自動重新排序後續訂單的ID，確保連續性
3. **備份建議**：建議在大量刪除前先備份 Google Sheets

### 🔧 技術考量
1. **快取清除**：刪除後會自動清除伺服器端快取
2. **前端更新**：前端會自動重新載入訂單列表
3. **錯誤處理**：完整的錯誤處理和用戶提示
4. **ID重排序**：自動重新排序後續訂單ID，確保資料一致性
5. **批量更新**：使用Google Sheets API的批量更新功能，提高效率

## 測試建議

1. **功能測試**：使用測試工具驗證刪除功能
2. **邊界測試**：測試刪除不存在的訂單 ID
3. **UI 測試**：確認前端確認對話框正常顯示
4. **資料一致性**：確認刪除後資料正確更新
5. **ID重排序測試**：驗證刪除後ID是否正確重新排序
6. **批量刪除測試**：測試連續刪除多個訂單的ID重排序效果

## 相關檔案

- `api/delete_order.php` - 後端刪除 API
- `src/components/OrderList.tsx` - 前端訂單列表組件
- `api/get_orders_from_sheet.php` - 訂單資料讀取 API
- `API_DOCUMENTATION.md` - API 文檔
- `test_delete_order.html` - 測試工具
- `DELETE_ORDER_UPGRADE.md` - 本說明文檔

## 版本資訊

- **升級日期**：2024年12月
- **影響範圍**：刪除訂單功能 + ID重排序功能
- **向後相容性**：API 介面保持相容，但行為有重大變更
- **新增功能**：ID自動重排序，確保訂單ID的連續性
- **建議動作**：更新前端確認邏輯，提醒用戶新的刪除行為
