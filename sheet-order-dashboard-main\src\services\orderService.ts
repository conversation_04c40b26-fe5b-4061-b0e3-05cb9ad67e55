import { Order, OrderStats, OrderItem } from '@/types/order';
// src/services/orderService.ts

// 根據環境動態設置 API 基礎路徑
// 使用全局配置或默認值
const getApiBase = () => {
  // 檢查是否有全局配置
  if (window.API_CONFIG && typeof window.API_CONFIG.getApiBase === 'function') {
    return window.API_CONFIG.getApiBase();
  }

  // 檢查當前環境（備用方案）
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const port = window.location.port;

  // 本地開發環境（localhost:8080 指向 htdocs，需要完整路徑）
  if (isLocalhost && port === '8080') {
    return '/sheet-order-dashboard-main/api';
  }

  // Cloudflare Tunnels 環境（node.767780.xyz 直接指向 sheet-order-dashboard-main 目錄）
  // 所以 API 路徑就是 /api
  return '/api';
};

const API_BASE = getApiBase();

// 輸出當前使用的 API 路徑，方便調試
console.log('當前 API 路徑:', API_BASE);

// 快取機制
interface OrderCache {
  timestamp: number;
  data: Order[];
  filters?: {
    status?: string;
    deliveryMethod?: string;
    search?: string;
    date?: string;
    paymentStatus?: string;
  };
}

let orderCache: OrderCache | null = null;
const CACHE_DURATION = 15000; // 快取有效期 15 秒，降低以提高即時性

// 直接從 Google Sheets API 取得訂單，不再使用 mockOrders
export const fetchOrders = async (filters?: {
  status?: string;
  deliveryMethod?: string;
  search?: string;
  date?: string;
  paymentStatus?: string;
}): Promise<Order[]> => {
  // 檢查是否有快取且未過期
  const now = Date.now();

  // 如果沒有進行搜尋或篩選，且有快取且未過期，直接使用快取資料
  if (
    orderCache &&
    (now - orderCache.timestamp < CACHE_DURATION) &&
    (!filters || (!filters.status && !filters.deliveryMethod && !filters.search && !filters.date && !filters.paymentStatus))
  ) {
    console.log('使用快取的訂單資料');

    // 有過濾條件時，在前端篩選快取中的資料
    if (filters) {
      return filterOrdersInMemory(orderCache.data, filters);
    }

    return orderCache.data;
  }

  // 從後端 API 取得 Google Sheets 訂單，添加隨機參數防止 Cloudflare 快取
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);

  // 添加多個隨機參數，確保每次請求都是唯一的
  const url = new URL(`${window.location.origin}${API_BASE}/get_orders_from_sheet.php`);
  url.searchParams.append('refresh', '1');
  url.searchParams.append('_', timestamp.toString());
  url.searchParams.append('nonce', nonce);
  url.searchParams.append('v', '1.1'); // API 版本號

  // 使用 no-store 快取策略
  const res = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
  if (!res.ok) {
    // 如果 HTTP 狀態碼不是 2xx，嘗試讀取錯誤訊息
    let errorMsg = '讀取訂單失敗';
    try {
      const errorResult = await res.json();
      errorMsg = errorResult.message || errorMsg;
    } catch (e) {
      // 如果回應不是 JSON 或其他錯誤，使用 res.statusText
      errorMsg = `讀取訂單失敗: ${res.statusText}`;
    }
    throw new Error(errorMsg);
  }

  const result = await res.json();
  if (!result.success) throw new Error(result.message || '讀取訂單失敗');
  if (!result.data || !Array.isArray(result.data)) {
    console.warn('API回傳的訂單資料格式不正確，應為陣列:', result.data);
    return []; // 或者拋出錯誤，視情況而定
  }

  // 將 Google Sheets 資料轉換成前端 Order 型別
  let orders = result.data.map((row: {
    createdAt?: string;
    id?: string;
    orderNumber?: string;
    customerName?: string;
    customerPhone?: string;
    items?: string | Array<{product: string; quantity: number; price: number}>;
    amount?: number;
    dueDate?: string;
    deliveryTime?: string;
    note?: string;
    status?: string;
    deliveryMethod?: string;
    deliveryAddress?: string;
    paymentMethod?: string;
    paymentStatus?: string;
    備註?: string;
    訂單時間?: string;
    款項?: string;
  }, idx: number) => {
    const createdAt = String(row['createdAt'] || row['訂單時間'] || row[0] || new Date().toISOString().split('T')[0]);
    const id = String(row.id || `generated_id_${idx}`); // 提供預設ID以防萬一
    const orderNumber = String(row.orderNumber || `ORD-${Date.now()}-${idx}`); // 提供預設訂單號
    const customerName = String(row.customerName || '');
    const customerPhone = String(row.customerPhone || '');

    let itemsArray: { product: string; quantity: number; price: number; subtotal: number }[] = [];
    if (typeof row.items === 'string' && row.items.trim() !== '') {
      itemsArray = row.items.split(',').map((itemStr: string) => {
        const parts = itemStr.split(' x ');
        const product = parts[0] ? parts[0].trim() : '未知商品';
        const quantity = Number(parts[1]) || 1;
        let price = 0;
        // 自動對應單價
        if (product.includes('原味蘿蔔糕')) price = 250;
        else if (product.includes('芋頭粿')) price = 350;
        else if (product.includes('港式蘿蔔糕')) price = 350;
        // 如果 Google Sheet 提供單價，則使用提供的單價
        if (parts.length > 2 && parts[2] && !isNaN(Number(parts[2]))) {
            price = Number(parts[2]);
        }
        return {
          product,
          quantity,
          price,
          subtotal: price * quantity
        };
      });
    } else if (Array.isArray(row.items)) {
      // 如果 items 已經是陣列格式 (雖然目前邏輯是字串，但增加彈性)
      itemsArray = row.items.map((item: { product?: string; quantity?: number; price?: number }) => {
        let price = Number(item.price);
        let quantity = Number(item.quantity);
        price = isNaN(price) || price < 0 ? 0 : price;
        quantity = isNaN(quantity) || quantity < 0 ? 0 : quantity;
        return {
          product: String(item.product || '未知商品'),
          quantity,
          price,
          subtotal: price * quantity
        };
      });
    }

    // 嘗試將各種日期格式轉換為 YYYY-MM-DD
    let formattedDueDate = '';
    if (row.dueDate) {
      try {
        const dateObj = new Date(String(row.dueDate).replace(/-/g, '/'));
        if (!isNaN(dateObj.getTime())) {
          const year = dateObj.getFullYear();
          const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
          const day = dateObj.getDate().toString().padStart(2, '0');
          formattedDueDate = `${year}-${month}-${day}`;
        }
      } catch (e) {
        console.warn(`無法解析日期: ${row.dueDate}`);
      }
    }

    return {
      createdAt,
      id,
      orderNumber,
      customer: {
        name: customerName,
        phone: customerPhone
      },
      items: itemsArray,
      total: Number(row.amount) || 0,
      dueDate: formattedDueDate,
      deliveryTime: String(row.deliveryTime || ''),
      notes: String(row.note || row['note'] || row['備註'] || ''),
      status: String(row.status || '訂單確認中'), // 提供預設狀態
      deliveryMethod: String(row.deliveryMethod || ''),
      deliveryAddress: String(row.deliveryAddress || ''),
      paymentMethod: String(row.paymentMethod || ''),
      paymentStatus: String(row.paymentStatus || row['paymentStatus'] || row['款項'] || '')
    };
  });

  // 更新快取
  orderCache = {
    timestamp: now,
    data: orders,
    filters: filters ? { ...filters } : undefined
  };

  // 有過濾條件時前端進行過濾
  if (filters) {
    orders = filterOrdersInMemory(orders, filters);
  }

  return orders;
};

// 在記憶體中過濾訂單資料的函數
const filterOrdersInMemory = (orders: Order[], filters: {
  status?: string;
  deliveryMethod?: string;
  search?: string;
  date?: string;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
  paymentStatus?: string;
}): Order[] => {
  let filteredOrders = [...orders];

  if (filters.status && filters.status !== '所有狀態') {
    filteredOrders = filteredOrders.filter(order => order.status === filters.status);
  }

  // 配送方式篩選
  if (filters.deliveryMethod && filters.deliveryMethod !== '所有配送方式') {
    filteredOrders = filteredOrders.filter(order => order.deliveryMethod === filters.deliveryMethod);
  }

  if (filters.search) {
    const searchTerm = String(filters.search).toLowerCase();
    filteredOrders = filteredOrders.filter(
      order =>
        (order.orderNumber && order.orderNumber.toLowerCase().includes(searchTerm)) ||
        (order.customer.name && order.customer.name.toLowerCase().includes(searchTerm)) ||
        (order.customer.phone && order.customer.phone.includes(searchTerm))
    );
  }

  // 到貨日期篩選 - 支援日期區間和單一日期（向後兼容）
  if (filters.dateRange || filters.date) {
    try {
      let startDateFilter: Date | undefined;
      let endDateFilter: Date | undefined;

      // 優先使用日期區間，如果沒有則使用單一日期（向後兼容）
      if (filters.dateRange) {
        if (filters.dateRange.startDate) {
          startDateFilter = new Date(filters.dateRange.startDate);
          startDateFilter.setHours(0, 0, 0, 0);
        }
        if (filters.dateRange.endDate) {
          endDateFilter = new Date(filters.dateRange.endDate);
          endDateFilter.setHours(23, 59, 59, 999); // 結束日期設為當天最後一刻
        }
      } else if (filters.date) {
        // 向後兼容：單一日期篩選（選擇日期或之後的訂單）
        startDateFilter = new Date(filters.date);
        startDateFilter.setHours(0, 0, 0, 0);
      }

      if (startDateFilter || endDateFilter) {
        filteredOrders = filteredOrders.filter(order => {
          if (!order.dueDate) return false; // 如果訂單沒有到貨日期，則不符合條件

          try {
            const orderDueDate = new Date(order.dueDate);
            orderDueDate.setHours(0, 0, 0, 0);

            if (isNaN(orderDueDate.getTime())) return false;

            // 檢查開始日期條件
            if (startDateFilter && !isNaN(startDateFilter.getTime())) {
              if (orderDueDate.getTime() < startDateFilter.getTime()) {
                return false;
              }
            }

            // 檢查結束日期條件
            if (endDateFilter && !isNaN(endDateFilter.getTime())) {
              if (orderDueDate.getTime() > endDateFilter.getTime()) {
                return false;
              }
            }

            return true;
          } catch (e) {
            console.warn(`過濾時無法解析訂單到貨日期: ${order.dueDate}`);
            return false;
          }
        });
      }
    } catch (e) {
      console.warn(`過濾時無法解析篩選日期:`, e);
    }
  }

  // 付款狀態篩選
  if (filters.paymentStatus && filters.paymentStatus !== '所有付款狀態') {
    filteredOrders = filteredOrders.filter(order => order.paymentStatus === filters.paymentStatus);
  }

  return filteredOrders;
};

export const fetchOrderById = async (id: string): Promise<Order | null> => {
  const orders = await fetchOrders();
  return orders.find(order => order.id === id) || null;
};

// 清除訂單快取，強制重新從服務器獲取最新數據
export const clearOrderCache = () => {
  orderCache = null;
};

export const fetchOrderStats = async (): Promise<OrderStats> => {
  const orders = await fetchOrders();

  // 計算未收費訂單數量（款項狀態為空、未收費或未全款）
  const unpaidOrders = orders.filter(order =>
    !order.paymentStatus ||
    order.paymentStatus === '未收費' ||
    order.paymentStatus === '未全款'
  );

  // 計算所有訂單總金額
  const totalAmount = orders.reduce((sum, order) => sum + (order.total || 0), 0);

  return {
    total: orders.length,
    pending: orders.filter(order => order.status === '訂單確認中').length,
    processing: orders.filter(order => order.status === '已抄單').length,
    completed: orders.filter(order => order.status === '已出貨').length,
    canceled: orders.filter(order => order.status === '取消訂單').length,
    unpaid: unpaidOrders.length,
    totalAmount: totalAmount
  };
};

// 注意：Google Sheets API 不支援直接修改資料，若需更新請自行設計後端 API 處理
export const updateOrderStatus = async (id: string, status: '訂單確認中' | '已抄單' | '已出貨' | '取消訂單'): Promise<void> => {
  // 添加時間戳和隨機數，確保每次請求都是唯一的
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);

  // 構建 URL 並添加參數
  const url = new URL(`${window.location.origin}${API_BASE}/update_order_status.php`);
  url.searchParams.append('_', timestamp.toString());
  url.searchParams.append('nonce', nonce);

  const res = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    body: JSON.stringify({ id, status, timestamp, nonce }),
  });
  if (!res.ok) {
    let errorMsg = '更新訂單狀態失敗';
    try {
      const errorResult = await res.json();
      errorMsg = errorResult.message || errorMsg;
    } catch (e) {
      errorMsg = `更新訂單狀態失敗: ${res.statusText}`;
    }
    throw new Error(errorMsg);
  }
  const result = await res.json();
  if (!result.success) throw new Error(result.message || '更新訂單狀態失敗');

  // 成功更新後清除快取
  clearOrderCache();
};

// 批次更新訂單狀態
export const batchUpdateOrderStatus = async (ids: string[], status: '訂單確認中' | '已抄單' | '已出貨' | '取消訂單'): Promise<void> => {
  // 使用 Promise.all 實現併發請求，提高批次處理效率
  try {
    await Promise.all(ids.map(id => updateOrderStatus(id, status)));

    // 批次操作成功後清除快取
    clearOrderCache();
  } catch (error) {
    console.error('批次更新訂單狀態失敗:', error);
    throw error;
  }
};

// 批次更新款項狀態
export const updateOrderPaymentStatus = async (id: string, paymentStatus: string): Promise<void> => {
  // 添加時間戳和隨機數，確保每次請求都是唯一的
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);

  // 構建 URL 並添加參數
  const url = new URL(`${window.location.origin}${API_BASE}/update_payment_status.php`);
  url.searchParams.append('_', timestamp.toString());
  url.searchParams.append('nonce', nonce);

  const res = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    body: JSON.stringify({ id, paymentStatus, timestamp, nonce }),
  });
  if (!res.ok) {
    let errorMsg = '更新款項狀態失敗';
    try {
      const errorResult = await res.json();
      errorMsg = errorResult.message || errorMsg;
    } catch (e) {
      errorMsg = `更新款項狀態失敗: ${res.statusText}`;
    }
    throw new Error(errorMsg);
  }
  const result = await res.json();
  if (!result.success) throw new Error(result.message || '更新款項狀態失敗');

  // 成功更新後清除快取
  clearOrderCache();
};

export const batchUpdateOrderPaymentStatus = async (ids: string[], paymentStatus: string): Promise<void> => {
  // 使用 Promise.all 實現併發請求，提高批次處理效率
  try {
    await Promise.all(ids.map(id => updateOrderPaymentStatus(id, paymentStatus)));

    // 批次操作成功後清除快取
    clearOrderCache();
  } catch (error) {
    console.error('批次更新款項狀態失敗:', error);
    throw error;
  }
};

// 更新訂單商品
export const updateOrderItems = async (id: string, items: OrderItem[], total: number): Promise<void> => {
  // 添加時間戳和隨機數，確保每次請求都是唯一的
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);

  // 構建 URL 並添加參數
  const url = new URL(`${window.location.origin}${API_BASE}/update_order_items.php`);
  url.searchParams.append('_', timestamp.toString());
  url.searchParams.append('nonce', nonce);

  const res = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    body: JSON.stringify({ id, items, total, timestamp, nonce }),
  });

  if (!res.ok) {
    let errorMsg = '更新訂單商品失敗';
    try {
      const errorResult = await res.json();
      errorMsg = errorResult.message || errorMsg;
    } catch (e) {
      errorMsg = `更新訂單商品失敗: ${res.statusText}`;
    }
    throw new Error(errorMsg);
  }

  const result = await res.json();
  if (!result.success) throw new Error(result.message || '更新訂單商品失敗');

  // 成功更新後清除快取
  clearOrderCache();
};

// 刪除訂單
export const deleteOrder = async (id: string): Promise<any> => {
  // 添加時間戳和隨機數，確保每次請求都是唯一的
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);

  // 構建 URL 並添加參數
  const url = new URL(`${window.location.origin}${API_BASE}/delete_order.php`);
  url.searchParams.append('_', timestamp.toString());
  url.searchParams.append('nonce', nonce);

  // 處理刪除訂單的邏輯
  const res = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    body: JSON.stringify({ id, timestamp, nonce }),
  });
  if (!res.ok) {
    let errorMsg = '刪除訂單失敗';
    try {
      const errorResult = await res.json();
      errorMsg = errorResult.message || errorMsg;
    } catch (e) {
      errorMsg = `刪除訂單失敗: ${res.statusText}`;
    }
    throw new Error(errorMsg);
  }
  const result = await res.json();
  if (!result.success) throw new Error(result.message || '刪除訂單失敗');

  // 成功刪除後清除快取
  clearOrderCache();

  // 返回完整的結果，包含重排序信息
  return result;
};

export const generatePrintData = (orders: Order[]): {
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  items: string;
  total: number;
  deliveryMethod: string;
  deliveryAddress: string;
  dueDate: string;
  deliveryTime: string;
  paymentMethod: string;
  notes: string;
}[] => {
  // Transform orders into print format
  return orders.map(order => ({
    orderNumber: order.orderNumber,
    customerName: order.customer.name,
    customerPhone: order.customer.phone,
    items: order.items.map(item => `${item.product} x ${item.quantity}`).join(', '),
    total: order.total,
    deliveryMethod: order.deliveryMethod,
    deliveryAddress: order.deliveryAddress,
    dueDate: order.dueDate,
    deliveryTime: order.deliveryTime,
    paymentMethod: order.paymentMethod,
    notes: order.notes
  }));
};

export const exportToCsv = (orders: Order[]): string => {
  // 黑貓宅急便格式標題
  /*const headers = [
    '訂單編號',
    '溫層',
    '規格',
    '代收貨款',
    '收件人-姓名',
    '收件人-電話',
    '收件人-地址',
    '寄件人-姓名',
    '寄件人-電話',
    '寄件人-地址',
    '出貨日期',
    '希望配達日',
    '希望配合時段',
    '品類代碼',
    '品名',
    '易碎物品',
    '備註'
  ].join(',');*/

  // 固定寄件人資訊
  const senderName = '曾炳傑';
  const senderPhone = '0937292815';
  const senderAddress = '雲林縣西螺鎮中山路302-3號';

  // 去除特殊符號工具
  const removeSpecialChars = (str: string) => str.replace(/[^\u4e00-\u9fa5A-Za-z0-9]/g, '');
  // 只保留 09 開頭的電話
  const formatPhone = (phone: string) => /^09\d{8}$/.test(phone) ? `'${phone}` : '';
  // CSV欄位格式化工具，處理包含逗號、引號、換行的內容
  const formatCsvField = (str: string) => {
    if (!str) return '';
    // 如果包含逗號、引號或換行符，需要用引號包圍並轉義內部引號
    if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  const today = new Date();
  const pad = (n: number) => n.toString().padStart(2, '0');
  const todayStr = `${today.getFullYear()}${pad(today.getMonth() + 1)}${pad(today.getDate())}`;

  // 依勾選順序自動產生訂單編號（A001~A100）
  const genOrderNumber = (idx: number) => `A${(idx + 1).toString().padStart(3, '0')}`;

  const rows = orders.map((order, idx) => {
    // 希望配達日格式化（假設 order.dueDate 是 yyyy-mm-dd 或 yyyy/mm/dd 或 Date 物件）
    let wishDate = '';
    if (order.dueDate) {
      const d = typeof order.dueDate === 'string' ? new Date(order.dueDate.replace(/-/g, '/')) : order.dueDate;
      if (!isNaN(d.getTime())) {
        // 檢查希望配達日是否在出貨日之前，如果是則設為出貨日+1
        if (d <= today) {
          const nextDay = new Date(today);
          nextDay.setDate(today.getDate() + 1);
          wishDate = `${nextDay.getFullYear()}${pad(nextDay.getMonth() + 1)}${pad(nextDay.getDate())}`;
        } else {
          wishDate = `${d.getFullYear()}${pad(d.getMonth() + 1)}${pad(d.getDate())}`;
        }
      }
    }
    // 如果沒有希望配達日，預設為出貨日+1
    if (!wishDate) {
      const nextDay = new Date(today);
      nextDay.setDate(today.getDate() + 1);
      wishDate = `${nextDay.getFullYear()}${pad(nextDay.getMonth() + 1)}${pad(nextDay.getDate())}`;
    }
    // 希望配合時段
    let wishTime = '';
    if (order.deliveryTime) {
      if (order.deliveryTime.includes('上')) wishTime = '1';
      else if (order.deliveryTime.includes('下')) wishTime = '2';
    }
    return [
      genOrderNumber(idx), // 依序產生A001~A100訂單編號
      '2',               // 溫層（固定）
      '0',               // 規格（固定）
      order.paymentMethod === '貨到付款' ? order.total : '0', // 代收貨款
      removeSpecialChars(order.customer.name || ''),          // 收件人-姓名
      formatPhone(order.customer.phone || ''),                // 收件人-電話
      formatCsvField(order.deliveryAddress || ''),            // 收件人-地址
      senderName,       // 寄件人-姓名
      `'${senderPhone}`, // 寄件人-電話（強制文字格式）
      senderAddress,    // 寄件人-地址
      todayStr,         // 出貨日期
      wishDate,         // 希望配達日
      wishTime,         // 希望配合時段
      '0015',           // 品類代碼（固定）
      '蘿蔔糕',          // 品名（固定）
      'Y',              // 易碎物品（固定）
      formatCsvField(order.notes || '')                       // 備註
    ].join(',');
  });

  // 使用標準的Unicode (UTF-8)格式
  // 1. 不使用BOM標記，採用純UTF-8編碼
  // 2. 使用Windows標準的CRLF換行符
  // 3. 確保所有中文字符正確編碼
  //const BOM = '\uFEFF';
  const csvContent = rows.join('\r\n');//[headers, ...rows].join('\r\n');

  // 返回完整的CSV內容，包含BOM
  return  csvContent;//BOM +csvContent;
};
