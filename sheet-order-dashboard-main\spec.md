# 規格文件（spec.md）

## 一、專案簡介
蘿蔔糕訂購系統，主要功能為從 Google Sheet 取得訂單資料，並於網頁 Dashboard 呈現。前端採用 React + TypeScript + Tailwind CSS，後端以 PHP 串接 Google Sheets API，並設有快取與權限控管。

## 二、系統架構圖（UML）

### 1. 流程圖
```mermaid
flowchart TD
    User(使用者) -->|存取 Dashboard| Frontend(前端)
    Frontend -->|API 請求| Backend(後端 PHP)
    Backend -->|存取| Cache(快取)
    Backend -->|串接| GoogleSheet(Google Sheet API)
    GoogleSheet --> Backend
    Cache --> Backend
    Backend -->|回傳 JSON| Frontend
    Frontend -->|顯示訂單| User
```

### 2. 循序圖
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant G as GoogleSheet
    participant C as Cache
    U->>F: 輸入網址存取 Dashboard
    F->>B: 發送訂單查詢 API 請求
    B->>C: 檢查快取
    alt 有快取
      C-->>B: 回傳快取資料
    else 無快取
      B->>G: 取得 Google Sheet 訂單
      G-->>B: 回傳訂單資料
      B->>C: 寫入快取
    end
    B-->>F: 回傳訂單資料
    F-->>U: 呈現訂單
```

### 3. 物件關聯圖
```mermaid
classDiagram
    class Frontend {
      +fetchOrders()
      +renderTable()
    }
    class Backend {
      +getOrdersFromSheet()
      +handleCache()
      +apiResponse()
    }
    class Cache {
      +get()
      +set()
      +invalidate()
    }
    class GoogleSheet {
      +readOrders()
    }
    Frontend --> Backend : API 呼叫
    Backend --> Cache : 快取存取
    Backend --> GoogleSheet : API 串接
```

## 三、主要功能需求
1. 從 Google Sheet 取得訂單資料
2. 訂單資料快取與強制刷新
3. Dashboard 訂單列表顯示、搜尋、篩選
4. 權限驗證（如需登入）
5. 前後端錯誤處理

## 四、開發規範與流程
- 前端：React + TypeScript + Tailwind CSS，組件化設計，API 集成於 src/services
- 後端：PHP，PSR-12 風格，api/ 目錄下建立 API 端點，Google API 憑證放於根目錄
- 快取：cache/ 目錄，資料更新時自動清除
- 測試：前端 Jest，後端 Postman/curl
- 版本控制：Git，feature/bugfix 分支

## 五、部署
- 前端：npm run build，dist/ 上傳伺服器
- 後端：api/ 上傳伺服器，cache/ 權限設置

## 六、參考文件
- DEVELOPMENT_GUIDE.md
- API_DOCUMENTATION.md

---
本文件為專案開發規格依據，後續如有異動請同步更新。
