# Cloudflare 配置指南

為了確保通過 Cloudflare 域名訪問後台時能夠即時獲取最新數據，請按照以下步驟配置 Cloudflare：

## 1. 禁用 API 路徑的快取

1. 登錄 Cloudflare 控制面板
2. 選擇您的域名
3. 點擊 "頁面規則" (Page Rules)
4. 點擊 "創建頁面規則" (Create Page Rule)
5. 在 URL 欄位中輸入：`*yourdomain.com/api/*`
6. 添加以下設置：
   - 快取級別 (Cache Level): 繞過 (Bypass)
   - 瀏覽器快取 TTL (Browser Cache TTL): 0 秒
   - 邊緣快取 TTL (Edge Cache TTL): 0 秒
   - 始終使用 HTTPS: 開啟
7. 點擊 "保存並部署" (Save and Deploy)

## 2. 禁用開發工具路徑的快取

1. 創建另一個頁面規則
2. URL 欄位中輸入：`*yourdomain.com/sheet-order-dashboard-main/api/*`
3. 添加與上面相同的設置
4. 保存並部署

## 3. 配置開發者模式

1. 在 Cloudflare 控制面板中，點擊 "快取" (Cache)
2. 在 "配置" (Configuration) 部分，找到 "開發者模式" (Development Mode)
3. 暫時啟用開發者模式，這將完全繞過 Cloudflare 的快取
4. 測試您的應用程序是否能夠即時獲取數據
5. 測試完成後，您可以關閉開發者模式，依靠頁面規則來控制快取

## 4. 清除 Cloudflare 快取

如果您仍然遇到快取問題，可以嘗試清除 Cloudflare 的快取：

1. 在 Cloudflare 控制面板中，點擊 "快取" (Cache)
2. 點擊 "清除所有快取" (Purge Everything) 或 "自定義清除" (Custom Purge)
3. 如果選擇自定義清除，輸入以下 URL 模式：
   - `*yourdomain.com/api/*`
   - `*yourdomain.com/sheet-order-dashboard-main/api/*`
4. 點擊 "清除" (Purge)

## 5. 檢查瀏覽器快取

即使 Cloudflare 不快取內容，瀏覽器也可能快取 API 響應。在開發過程中，您可以：

1. 打開瀏覽器的開發者工具 (F12)
2. 右鍵點擊刷新按鈕，選擇 "清空快取並硬性重新載入" (Clear Cache and Hard Reload)
3. 在 Network 標籤中，勾選 "Disable cache" 選項

## 6. 使用 API 診斷工具

我們已經創建了一個 API 診斷工具，可以幫助您檢查 API 路徑和快取狀態：

1. 訪問 `/api/check_api_path.php`
2. 檢查響應中的 `environment` 和 `cache` 部分
3. 確認 `cache_dir_exists` 和 `cache_dir_writable` 都是 `true`

## 7. 監控 API 響應標頭

您可以使用瀏覽器的開發者工具監控 API 響應標頭：

1. 打開瀏覽器的開發者工具 (F12)
2. 切換到 Network 標籤
3. 發送一個 API 請求（例如，刪除訂單或更改狀態）
4. 點擊該請求，查看 Response Headers
5. 確認以下標頭存在且值正確：
   - `Cache-Control: no-store, no-cache, must-revalidate, max-age=0, s-maxage=0`
   - `Pragma: no-cache`
   - `Expires: 0`
   - `CF-Cache-Status: BYPASS` (如果通過 Cloudflare 訪問)

如果您按照上述步驟配置 Cloudflare，應該能夠解決通過 Cloudflare 域名訪問後台時無法即時獲取最新數據的問題。
